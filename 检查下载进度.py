#!/usr/bin/env python3
"""
检查下载进度
"""

import os
import json
from datetime import datetime

def check_download_progress():
    """检查下载进度"""
    print("📊 木星数据下载进度检查")
    print("=" * 60)
    
    # 获取总批次数
    try:
        with open('jupiter_download_batches.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        total_batches = len(data['batches'])
    except:
        print("❌ 无法读取批次文件")
        return
    
    print(f"总批次数: {total_batches}")
    
    # 检查结果文件
    result_files = [f for f in os.listdir('.') if f.startswith('download_results_') and f.endswith('.json')]
    result_files.sort()
    
    print(f"结果文件数: {len(result_files)}")
    
    completed_batches = set()
    total_points = 0
    
    for file in result_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            successful = 0
            points = 0
            
            for result in data['results']:
                if result['status'] == 'success':
                    completed_batches.add(result['batch_id'])
                    successful += 1
                    points += result['points']
            
            total_points += points
            print(f"   {file}: {successful}/{len(data['results'])} 成功, {points:,} 个点")
            
        except Exception as e:
            print(f"   ❌ {file}: 读取失败")
    
    # 检查批次文件
    batch_files = [f for f in os.listdir('planet_data') if f.startswith('batch_') and f.endswith('.csv')]
    
    print(f"\n📁 批次文件:")
    print(f"   CSV文件数: {len(batch_files)}")
    
    # 总结
    print(f"\n📈 进度总结:")
    print(f"   完成批次: {len(completed_batches)}/{total_batches}")
    print(f"   完成率: {len(completed_batches)/total_batches*100:.1f}%")
    print(f"   总数据点: {total_points:,}")
    
    # 检查是否有最终文件
    final_file = 'planet_data/木星_严格时间序列_完整数据.csv'
    if os.path.exists(final_file):
        import pandas as pd
        try:
            df = pd.read_csv(final_file)
            print(f"   最终文件: ✅ ({len(df):,} 个点)")
        except:
            print(f"   最终文件: ❌ (损坏)")
    else:
        print(f"   最终文件: ⏳ (未生成)")
    
    # 显示缺失的批次
    missing_batches = []
    for i in range(1, total_batches + 1):
        if i not in completed_batches:
            missing_batches.append(i)
    
    if missing_batches:
        print(f"\n⚠️ 缺失批次 ({len(missing_batches)} 个):")
        if len(missing_batches) <= 20:
            print(f"   {missing_batches}")
        else:
            print(f"   {missing_batches[:10]} ... {missing_batches[-10:]}")
    else:
        print(f"\n✅ 所有批次已完成！")

if __name__ == "__main__":
    check_download_progress()
