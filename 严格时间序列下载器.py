#!/usr/bin/env python3
"""
基于严格木星日时间序列的精确下载器
确保每个数据点都严格按照预定时间序列下载
"""

import os
import json
import pandas as pd
import numpy as np
import warnings
import ssl
import urllib3
import time
from datetime import datetime, timedelta

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

class PreciseJupiterDownloader:
    def __init__(self):
        """初始化精确下载器"""
        self.sequence_file = 'jupiter_time_sequence.json'
        self.batches_file = 'jupiter_download_batches.json'
        self.output_dir = 'planet_data'
        self.final_file = 'planet_data/木星_严格时间序列_完整数据.csv'
        self.failed_batches_file = 'failed_batches.json'
        self.missing_points_file = 'missing_time_points.json'
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 加载时间序列和批次信息
        self.load_time_sequence()
        self.load_download_batches()
        
    def load_time_sequence(self):
        """加载时间序列"""
        print(f"📖 加载时间序列: {self.sequence_file}")
        
        try:
            with open(self.sequence_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.metadata = data['metadata']
            self.time_sequence = data['time_sequence']
            
            print(f"✅ 时间序列加载成功")
            print(f"   总时间点: {len(self.time_sequence):,}")
            print(f"   时间范围: {self.metadata['start_time']} 到 {self.metadata['end_time']}")
            print(f"   木星日长度: {self.metadata['jupiter_day_hours']} 小时")
            
        except FileNotFoundError:
            print(f"❌ 时间序列文件不存在: {self.sequence_file}")
            print("请先运行 生成严格木星日时间序列.py")
            raise
    
    def load_download_batches(self):
        """加载下载批次"""
        print(f"📖 加载下载批次: {self.batches_file}")
        
        try:
            with open(self.batches_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.batch_metadata = data['metadata']
            self.batches = data['batches']
            
            print(f"✅ 下载批次加载成功")
            print(f"   总批次数: {len(self.batches)}")
            print(f"   平均每批次: {self.batch_metadata['total_time_points'] / len(self.batches):.1f} 个时间点")
            
        except FileNotFoundError:
            print(f"❌ 下载批次文件不存在: {self.batches_file}")
            print("请先运行 生成严格木星日时间序列.py")
            raise
    
    def download_single_batch(self, batch):
        """下载单个批次"""
        batch_id = batch['batch_id']
        start_time = batch['start_time']
        end_time = batch['end_time']
        expected_points = batch['time_points']
        
        print(f"📦 批次 {batch_id}: {start_time[:19]} 到 {end_time[:19]} (期望 {expected_points} 个点)")
        
        try:
            from astroquery.jplhorizons import Horizons
            
            # 使用精确的时间范围
            jupiter = Horizons(
                id='599',  # 木星
                location='@sun',
                epochs={
                    'start': batch['jpl_start_date'],
                    'stop': batch['jpl_end_date'],
                    'step': '596m'  # 使用最接近的JPL格式
                }
            )
            
            vectors = jupiter.vectors()
            df = vectors.to_pandas()
            
            if len(df) > 0:
                processed_df = self.process_data(df)
                
                print(f"   ✅ 成功获取 {len(processed_df)} 个数据点")
                
                # 保存批次数据
                batch_file = f"{self.output_dir}/batch_{batch_id:03d}.csv"
                processed_df.to_csv(batch_file, index=False, encoding='utf-8-sig')
                
                return {
                    'batch_id': batch_id,
                    'status': 'success',
                    'points_downloaded': len(processed_df),
                    'points_expected': expected_points,
                    'file': batch_file,
                    'start_time': start_time,
                    'end_time': end_time
                }
            else:
                print(f"   ⚠️ 无数据返回")
                return {
                    'batch_id': batch_id,
                    'status': 'no_data',
                    'points_downloaded': 0,
                    'points_expected': expected_points,
                    'error': 'No data returned',
                    'start_time': start_time,
                    'end_time': end_time
                }
                
        except Exception as e:
            print(f"   ❌ 下载失败: {e}")
            return {
                'batch_id': batch_id,
                'status': 'failed',
                'points_downloaded': 0,
                'points_expected': expected_points,
                'error': str(e),
                'start_time': start_time,
                'end_time': end_time
            }
    
    def process_data(self, df):
        """处理原始数据"""
        # 单位转换常数
        au_to_km = 149597870.7
        day_to_sec = 86400
        light_speed_au_per_sec = 1.0 / 499.004783836
        
        processed_df = pd.DataFrame()
        
        # 基本列
        processed_df['julian_date'] = df['datetime_jd']
        processed_df['date'] = df['datetime_str']
        processed_df['x_position_au'] = df['x']
        processed_df['y_position_au'] = df['y']
        processed_df['z_position_au'] = df['z']
        
        # 速度转换 (AU/day -> km/s)
        processed_df['x_velocity_km_s'] = df['vx'] * au_to_km / day_to_sec
        processed_df['y_velocity_km_s'] = df['vy'] * au_to_km / day_to_sec
        processed_df['z_velocity_km_s'] = df['vz'] * au_to_km / day_to_sec
        
        # 距离
        processed_df['distance_au'] = df['range']
        processed_df['distance_km'] = df['range'] * au_to_km
        
        # 光时 (秒)
        processed_df['light_time_sec'] = df['range'] / light_speed_au_per_sec
        
        # 距离变化率 (AU/day -> km/s)
        processed_df['range_rate_km_s'] = df['range_rate'] * au_to_km / day_to_sec
        
        # 轨道速度 (km/s)
        processed_df['speed_km_s'] = np.sqrt(
            processed_df['x_velocity_km_s']**2 + 
            processed_df['y_velocity_km_s']**2 + 
            processed_df['z_velocity_km_s']**2
        )
        
        return processed_df
    
    def download_all_batches(self):
        """下载所有批次"""
        print(f"\n🚀 开始批次下载")
        print("=" * 60)
        
        download_results = []
        successful_batches = 0
        failed_batches = []
        total_points_downloaded = 0
        
        for i, batch in enumerate(self.batches, 1):
            print(f"\n进度: {i}/{len(self.batches)}")
            
            result = self.download_single_batch(batch)
            download_results.append(result)
            
            if result['status'] == 'success':
                successful_batches += 1
                total_points_downloaded += result['points_downloaded']
            else:
                failed_batches.append(result)
            
            # 批次间延迟
            if i < len(self.batches):
                print(f"   ⏳ 等待2秒...")
                time.sleep(2)
        
        # 保存下载结果
        results_file = 'download_results.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': {
                    'total_batches': len(self.batches),
                    'successful_batches': successful_batches,
                    'failed_batches': len(failed_batches),
                    'total_points_downloaded': total_points_downloaded,
                    'expected_total_points': self.batch_metadata['total_time_points'],
                    'download_time': datetime.now().isoformat()
                },
                'results': download_results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 下载完成统计:")
        print(f"   成功批次: {successful_batches}/{len(self.batches)}")
        print(f"   失败批次: {len(failed_batches)}")
        print(f"   下载数据点: {total_points_downloaded:,}")
        print(f"   期望数据点: {self.batch_metadata['total_time_points']:,}")
        print(f"   完成率: {total_points_downloaded/self.batch_metadata['total_time_points']*100:.2f}%")
        
        # 保存失败批次信息
        if failed_batches:
            with open(self.failed_batches_file, 'w', encoding='utf-8') as f:
                json.dump(failed_batches, f, indent=2, ensure_ascii=False)
            print(f"   失败批次信息已保存: {self.failed_batches_file}")
        
        return download_results, failed_batches
    
    def merge_batch_files(self, download_results):
        """合并所有批次文件"""
        print(f"\n🔄 合并批次文件")
        print("=" * 60)
        
        all_dataframes = []
        successful_files = 0
        
        for result in download_results:
            if result['status'] == 'success' and 'file' in result:
                try:
                    df = pd.read_csv(result['file'])
                    all_dataframes.append(df)
                    successful_files += 1
                    print(f"   ✅ 加载批次 {result['batch_id']}: {len(df)} 个点")
                except Exception as e:
                    print(f"   ❌ 加载批次 {result['batch_id']} 失败: {e}")
        
        if all_dataframes:
            # 合并所有数据
            combined_df = pd.concat(all_dataframes, ignore_index=True)
            
            # 按时间排序
            combined_df['datetime'] = pd.to_datetime(combined_df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
            combined_df = combined_df.sort_values('datetime').reset_index(drop=True)
            combined_df = combined_df.drop('datetime', axis=1)
            
            # 去重
            original_length = len(combined_df)
            combined_df = combined_df.drop_duplicates(subset=['julian_date'], keep='first')
            duplicates_removed = original_length - len(combined_df)
            
            # 保存最终数据
            combined_df.to_csv(self.final_file, index=False, encoding='utf-8-sig')
            
            print(f"\n✅ 数据合并完成")
            print(f"   成功文件: {successful_files}")
            print(f"   合并数据点: {len(combined_df):,}")
            print(f"   移除重复: {duplicates_removed}")
            print(f"   最终文件: {self.final_file}")
            
            return combined_df
        else:
            print(f"❌ 没有成功的批次文件可合并")
            return None
    
    def analyze_time_alignment(self, df):
        """分析时间对齐情况"""
        print(f"\n🔍 分析时间对齐情况")
        print("=" * 60)
        
        if df is None or len(df) == 0:
            print("❌ 无数据可分析")
            return
        
        # 转换时间
        df['datetime'] = pd.to_datetime(df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
        df = df.sort_values('datetime').reset_index(drop=True)
        
        # 与理论时间序列对比
        print(f"📊 时间对齐分析:")
        print(f"   下载数据点: {len(df):,}")
        print(f"   理论数据点: {len(self.time_sequence):,}")
        print(f"   覆盖率: {len(df)/len(self.time_sequence)*100:.3f}%")
        
        # 检查时间间隔
        if len(df) > 1:
            time_diffs = df['datetime'].diff().dropna()
            time_diffs_hours = time_diffs.dt.total_seconds() / 3600
            
            target_interval = self.metadata['jupiter_day_hours']
            
            print(f"\n   时间间隔分析:")
            print(f"   目标间隔: {target_interval:.6f} 小时")
            print(f"   平均间隔: {time_diffs_hours.mean():.6f} 小时")
            print(f"   标准差: {time_diffs_hours.std():.6f} 小时")
            
            # 检查标准间隔比例
            tolerance = 0.1  # 6分钟容差
            standard_intervals = time_diffs_hours[
                np.abs(time_diffs_hours - target_interval) < tolerance
            ]
            
            standard_percentage = len(standard_intervals) / len(time_diffs_hours) * 100
            print(f"   标准间隔比例: {standard_percentage:.2f}%")
            
            if standard_percentage >= 95:
                print(f"   ✅ 时间对齐优秀！")
            elif standard_percentage >= 90:
                print(f"   📈 时间对齐良好")
            else:
                print(f"   ⚠️ 时间对齐需要改进")
    
    def retry_failed_batches(self, failed_batches, max_retries=2):
        """重试失败的批次"""
        if not failed_batches:
            print(f"\n✅ 没有失败的批次需要重试")
            return []
        
        print(f"\n🔄 重试失败的批次")
        print("=" * 60)
        print(f"失败批次数: {len(failed_batches)}")
        
        still_failed = []
        
        for retry_round in range(1, max_retries + 1):
            print(f"\n第 {retry_round} 轮重试:")
            
            current_failed = failed_batches if retry_round == 1 else still_failed
            still_failed = []
            
            for failed_batch in current_failed:
                batch_id = failed_batch['batch_id']
                
                # 找到原始批次信息
                original_batch = None
                for batch in self.batches:
                    if batch['batch_id'] == batch_id:
                        original_batch = batch
                        break
                
                if original_batch:
                    print(f"   重试批次 {batch_id}...")
                    result = self.download_single_batch(original_batch)
                    
                    if result['status'] != 'success':
                        still_failed.append(result)
                    
                    time.sleep(3)  # 重试间隔更长
        
        if still_failed:
            print(f"\n⚠️ 仍有 {len(still_failed)} 个批次失败")
            # 更新失败批次文件
            with open(self.failed_batches_file, 'w', encoding='utf-8') as f:
                json.dump(still_failed, f, indent=2, ensure_ascii=False)
        else:
            print(f"\n✅ 所有失败批次重试成功！")
        
        return still_failed
    
    def execute_precise_download(self):
        """执行精确下载"""
        print(f"🪐 基于严格时间序列的精确下载")
        print("=" * 60)
        print("特点：")
        print("1. 严格按照预生成的木星日时间序列")
        print("2. 批次间时间连续性保证")
        print("3. 失败批次自动重试")
        print("4. 完整的质量验证")
        
        # 1. 下载所有批次
        download_results, failed_batches = self.download_all_batches()
        
        # 2. 重试失败批次
        still_failed = self.retry_failed_batches(failed_batches)
        
        # 3. 合并数据
        final_df = self.merge_batch_files(download_results)
        
        # 4. 分析时间对齐
        self.analyze_time_alignment(final_df)
        
        # 5. 总结
        print(f"\n🎉 精确下载完成！")
        if final_df is not None:
            print(f"最终数据文件: {self.final_file}")
            print(f"数据点数: {len(final_df):,}")
            
            if len(still_failed) == 0:
                print(f"✅ 所有批次下载成功！")
            else:
                print(f"⚠️ {len(still_failed)} 个批次仍然失败")
        else:
            print(f"❌ 下载失败")

def main():
    """主函数"""
    print("🪐 严格时间序列木星数据下载器")
    print("=" * 60)
    
    try:
        downloader = PreciseJupiterDownloader()
        downloader.execute_precise_download()
    except Exception as e:
        print(f"❌ 下载器初始化失败: {e}")
        print("请确保已运行 生成严格木星日时间序列.py")

if __name__ == "__main__":
    main()
