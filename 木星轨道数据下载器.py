#!/usr/bin/env python3
"""
木星轨道数据下载器
支持多种时间间隔和周期选择
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
import ssl
import urllib3

# 忽略警告
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 处理SSL问题
ssl._create_default_https_context = ssl._create_unverified_context

class JupiterDataDownloader:
    def __init__(self):
        """初始化木星数据下载器"""
        self.configure_proxy()
        
        # 木星轨道参数（NASA官方数据）
        self.jupiter_params = {
            'id': '599',  # 木星JPL ID
            'orbital_period_years': 11.862,
            'orbital_period_days': 4332.59,
            'rotation_period_hours': 9.9250,  # NASA官方：9小时55分30秒（System III）
            'rotation_period_days': 9.9250 / 24,  # 0.4135416667天
            'mean_distance_au': 5.204,
            'eccentricity': 0.0489
        }
        
        # 下载方案（使用NASA官方精确数据）
        self.download_schemes = {
            '1': {
                'name': '测试方案：一年木星日间隔',
                'start': '2023-01-01',
                'end': '2024-01-01',
                'step': f"{self.jupiter_params['rotation_period_days']:.10f}d",
                'description': '1年数据，木星日间隔（9.925h），约883个数据点'
            },
            '2': {
                'name': '完整周期：木星日间隔 ⭐推荐',
                'start': '2023-01-01',
                'end': '2034-11-26',
                'step': f"{self.jupiter_params['rotation_period_days']:.10f}d",
                'description': '完整公转周期，木星日间隔（9.925h），约10,480个数据点'
            },
            '3': {
                'name': '完整周期：地球日间隔',
                'start': '2023-01-01',
                'end': '2034-11-26', 
                'step': '1d',
                'description': '完整公转周期，地球日间隔，约4,333个数据点'
            },
            '4': {
                'name': '半周期：地球日间隔',
                'start': '2023-01-01',
                'end': '2029-01-01',
                'step': '1d', 
                'description': '半个公转周期，地球日间隔，约2,191个数据点'
            }
        }
    
    def configure_proxy(self):
        """配置代理设置"""
        try:
            os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
            os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'
            print("✅ 代理配置完成: 127.0.0.1:7890")
        except Exception as e:
            print(f"⚠️ 代理配置警告: {e}")
    
    def show_schemes(self):
        """显示所有下载方案"""
        print("🪐 木星轨道数据下载方案")
        print("=" * 60)
        
        for key, scheme in self.download_schemes.items():
            print(f"{key}. {scheme['name']}")
            print(f"   时间范围: {scheme['start']} 到 {scheme['end']}")
            print(f"   时间步长: {scheme['step']}")
            print(f"   说明: {scheme['description']}")
            print()
        
        print("木星基本参数:")
        print(f"- 公转周期: {self.jupiter_params['orbital_period_years']:.3f} 年")
        print(f"- 自转周期: {self.jupiter_params['rotation_period_hours']:.3f} 小时")
        print(f"- 平均距离: {self.jupiter_params['mean_distance_au']:.3f} AU")
        print(f"- 轨道偏心率: {self.jupiter_params['eccentricity']:.4f}")
    
    def download_jupiter_data(self, scheme_key):
        """下载木星数据"""
        if scheme_key not in self.download_schemes:
            print(f"❌ 无效的方案编号: {scheme_key}")
            return None
        
        scheme = self.download_schemes[scheme_key]
        print(f"\n🚀 开始执行: {scheme['name']}")
        print(f"时间范围: {scheme['start']} 到 {scheme['end']}")
        print(f"时间步长: {scheme['step']}")
        print("-" * 50)
        
        try:
            from astroquery.jplhorizons import Horizons
            
            print("正在连接JPL HORIZONS系统...")
            
            # 创建木星查询对象
            jupiter = Horizons(
                id=self.jupiter_params['id'],
                location='@sun',  # 日心坐标系
                epochs={
                    'start': scheme['start'],
                    'stop': scheme['end'], 
                    'step': scheme['step']
                }
            )
            
            print("正在获取木星轨道数据...")
            vectors = jupiter.vectors()
            
            print("✅ 数据获取成功！")
            print(f"获取到 {len(vectors)} 个数据点")
            
            # 转换为DataFrame
            df = vectors.to_pandas()
            
            # 处理数据
            processed_df = self.process_jupiter_data(df, scheme)
            
            # 保存数据
            filename = self.save_data(processed_df, scheme)
            
            # 显示统计信息
            self.show_statistics(processed_df, scheme)
            
            return processed_df
            
        except Exception as e:
            print(f"❌ 下载失败: {e}")
            return None
    
    def process_jupiter_data(self, df, scheme):
        """处理木星数据"""
        print("正在处理数据...")
        
        # 单位转换常数
        au_to_km = 149597870.7
        day_to_sec = 86400
        light_speed_au_per_sec = 1.0 / 499.004783836
        
        # 创建处理后的DataFrame
        processed_df = pd.DataFrame()
        
        # 基本列
        processed_df['julian_date'] = df['datetime_jd']
        processed_df['date'] = df['datetime_str']
        processed_df['x_position_au'] = df['x']
        processed_df['y_position_au'] = df['y']
        processed_df['z_position_au'] = df['z']
        
        # 速度转换 (AU/day -> km/s)
        processed_df['x_velocity_km_s'] = df['vx'] * au_to_km / day_to_sec
        processed_df['y_velocity_km_s'] = df['vy'] * au_to_km / day_to_sec
        processed_df['z_velocity_km_s'] = df['vz'] * au_to_km / day_to_sec
        
        # 距离
        processed_df['distance_au'] = df['range']
        processed_df['distance_km'] = df['range'] * au_to_km
        
        # 光时 (秒)
        processed_df['light_time_sec'] = df['range'] / light_speed_au_per_sec
        
        # 距离变化率 (AU/day -> km/s)
        processed_df['range_rate_km_s'] = df['range_rate'] * au_to_km / day_to_sec
        
        # 轨道速度 (km/s)
        processed_df['speed_km_s'] = np.sqrt(
            processed_df['x_velocity_km_s']**2 + 
            processed_df['y_velocity_km_s']**2 + 
            processed_df['z_velocity_km_s']**2
        )
        
        return processed_df
    
    def save_data(self, df, scheme):
        """保存数据"""
        # 生成文件名
        scheme_name = scheme['name'].split('：')[0].replace(' ', '_')
        interval_type = '木星日' if 'rotation_period' in scheme['step'] else '地球日'
        
        filename = f"planet_data/木星_{scheme_name}_{interval_type}间隔_轨道数据.csv"
        
        # 确保目录存在
        os.makedirs('planet_data', exist_ok=True)
        
        # 保存数据
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"📁 数据已保存到: {filename}")
        
        return filename
    
    def show_statistics(self, df, scheme):
        """显示数据统计"""
        print(f"\n📊 {scheme['name']} 数据统计:")
        print(f"数据点数: {len(df)}")
        print(f"时间跨度: {scheme['start']} 到 {scheme['end']}")
        
        print(f"\n距离统计 (AU):")
        print(f"  平均距离: {df['distance_au'].mean():.4f}")
        print(f"  最近距离: {df['distance_au'].min():.4f}")
        print(f"  最远距离: {df['distance_au'].max():.4f}")
        print(f"  距离变化: {df['distance_au'].max() - df['distance_au'].min():.4f}")
        
        print(f"\n速度统计 (km/s):")
        print(f"  平均速度: {df['speed_km_s'].mean():.2f}")
        print(f"  最高速度: {df['speed_km_s'].max():.2f}")
        print(f"  最低速度: {df['speed_km_s'].min():.2f}")
        print(f"  速度变化: {df['speed_km_s'].max() - df['speed_km_s'].min():.2f}")
        
        print(f"\n光时统计:")
        print(f"  平均光时: {df['light_time_sec'].mean()/60:.1f} 分钟")
        print(f"  最短光时: {df['light_time_sec'].min()/60:.1f} 分钟")
        print(f"  最长光时: {df['light_time_sec'].max()/60:.1f} 分钟")

def main():
    """主函数"""
    downloader = JupiterDataDownloader()
    
    # 显示方案
    downloader.show_schemes()
    
    print("\n" + "="*60)
    print("请选择下载方案 (输入数字 1-4):")
    print("建议：")
    print("- 首次使用选择方案1进行测试")
    print("- 正式研究选择方案2（完整周期+木星日）")
    print("="*60)
    
    # 这里暂时不执行下载，等待用户确认
    print("\n⚠️ 注意：这是方案预览，实际下载请确认后执行")
    print("如需下载，请取消注释下面的代码并运行")
    
    # choice = input("请输入方案编号 (1-4): ").strip()
    # if choice in ['1', '2', '3', '4']:
    #     result = downloader.download_jupiter_data(choice)
    #     if result is not None:
    #         print(f"\n🎉 木星数据下载完成！")
    # else:
    #     print("❌ 无效选择")

if __name__ == "__main__":
    main()
