#!/usr/bin/env python3
"""
木星轨道数据最终下载器
使用596分钟步长（最接近木星日的可用格式）
从2024.12.31往前推一个木星公转周期
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
import ssl
import urllib3
import time

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

class JupiterFinalDownloader:
    def __init__(self):
        """初始化最终下载器"""
        print("🪐 木星轨道数据最终下载器")
        print("=" * 60)
        
        # 木星参数
        self.jupiter_params = {
            'id': '599',
            'orbital_period_days': 4332.59,
            'rotation_period_hours': 9.925,
            'step_minutes': 596  # 最接近木星日的可用步长
        }
        
        # 时间范围：从2024.12.31往前推一个公转周期
        self.end_date = datetime(2024, 12, 31)
        self.start_date = self.end_date - timedelta(days=self.jupiter_params['orbital_period_days'])
        
        # 步长格式
        self.step_size = f"{self.jupiter_params['step_minutes']}m"
        
        print(f"📅 时间范围:")
        print(f"开始: {self.start_date.strftime('%Y-%m-%d')}")
        print(f"结束: {self.end_date.strftime('%Y-%m-%d')}")
        print(f"总天数: {self.jupiter_params['orbital_period_days']:.1f}")
        print(f"步长: {self.step_size} (≈{self.jupiter_params['step_minutes']/60:.2f}小时)")
        print(f"目标木星日: {self.jupiter_params['rotation_period_hours']:.3f}小时")
        
        # 预计数据点数
        total_hours = self.jupiter_params['orbital_period_days'] * 24
        estimated_points = int(total_hours / (self.jupiter_params['step_minutes'] / 60))
        print(f"预计数据点: ~{estimated_points:,}")
        
    def download_in_batches(self):
        """分批下载数据"""
        print(f"\n🚀 开始分批下载...")
        print("=" * 50)
        
        # 计算批次（每批30天）
        batch_days = 30
        batches = []
        current_date = self.start_date
        
        while current_date < self.end_date:
            batch_end = min(current_date + timedelta(days=batch_days), self.end_date)
            batches.append({
                'start': current_date.strftime('%Y-%m-%d'),
                'end': batch_end.strftime('%Y-%m-%d'),
                'days': (batch_end - current_date).days
            })
            current_date = batch_end
        
        print(f"总批次: {len(batches)}")
        print(f"每批天数: {batch_days}")
        print(f"预计时间: {len(batches) * 1.5:.1f} 分钟")
        
        # 输出文件
        output_file = 'planet_data/木星_完整公转周期_596分钟间隔_轨道数据.csv'
        os.makedirs('planet_data', exist_ok=True)
        
        total_points = 0
        successful_batches = 0
        
        for i, batch in enumerate(batches, 1):
            try:
                print(f"\n📦 批次 {i}/{len(batches)}")
                print(f"时间: {batch['start']} 到 {batch['end']} ({batch['days']}天)")
                
                # 下载批次
                df = self.download_batch(batch)
                
                if df is not None and len(df) > 0:
                    # 写入文件
                    if i == 1:
                        df.to_csv(output_file, index=False, encoding='utf-8-sig')
                        print(f"📁 创建文件: {output_file}")
                    else:
                        df.to_csv(output_file, mode='a', header=False, index=False, encoding='utf-8-sig')
                        print(f"📁 追加数据")
                    
                    total_points += len(df)
                    successful_batches += 1
                    
                    print(f"✅ 本批次: {len(df)} 个数据点")
                    print(f"📊 累计: {total_points:,} 个数据点")
                    
                    # 显示进度
                    progress = (i / len(batches)) * 100
                    print(f"🔄 进度: {progress:.1f}%")
                    
                    # 批次间延迟
                    if i < len(batches):
                        print("⏳ 等待2秒...")
                        time.sleep(2)
                else:
                    print("⚠️ 本批次无数据")
                    
            except Exception as e:
                print(f"❌ 批次 {i} 失败: {e}")
                continue
        
        # 完成统计
        self.show_final_results(successful_batches, len(batches), total_points, output_file)
    
    def download_batch(self, batch_info):
        """下载单个批次"""
        try:
            from astroquery.jplhorizons import Horizons
            
            jupiter = Horizons(
                id=self.jupiter_params['id'],
                location='@sun',
                epochs={
                    'start': batch_info['start'],
                    'stop': batch_info['end'],
                    'step': self.step_size
                }
            )
            
            vectors = jupiter.vectors()
            df = vectors.to_pandas()
            processed_df = self.process_data(df)
            
            return processed_df
            
        except Exception as e:
            print(f"批次下载错误: {e}")
            return None
    
    def process_data(self, df):
        """处理数据，生成标准格式"""
        # 单位转换常数
        au_to_km = 149597870.7
        day_to_sec = 86400
        light_speed_au_per_sec = 1.0 / 499.004783836
        
        processed_df = pd.DataFrame()
        
        # 基本列
        processed_df['julian_date'] = df['datetime_jd']
        processed_df['date'] = df['datetime_str']
        processed_df['x_position_au'] = df['x']
        processed_df['y_position_au'] = df['y']
        processed_df['z_position_au'] = df['z']
        
        # 速度转换 (AU/day -> km/s)
        processed_df['x_velocity_km_s'] = df['vx'] * au_to_km / day_to_sec
        processed_df['y_velocity_km_s'] = df['vy'] * au_to_km / day_to_sec
        processed_df['z_velocity_km_s'] = df['vz'] * au_to_km / day_to_sec
        
        # 距离
        processed_df['distance_au'] = df['range']
        processed_df['distance_km'] = df['range'] * au_to_km
        
        # 光时 (秒)
        processed_df['light_time_sec'] = df['range'] / light_speed_au_per_sec
        
        # 距离变化率 (AU/day -> km/s)
        processed_df['range_rate_km_s'] = df['range_rate'] * au_to_km / day_to_sec
        
        # 轨道速度 (km/s)
        processed_df['speed_km_s'] = np.sqrt(
            processed_df['x_velocity_km_s']**2 + 
            processed_df['y_velocity_km_s']**2 + 
            processed_df['z_velocity_km_s']**2
        )
        
        return processed_df
    
    def show_final_results(self, successful_batches, total_batches, total_points, output_file):
        """显示最终结果"""
        print(f"\n" + "=" * 60)
        print("🎉 木星数据下载完成！")
        print("=" * 60)
        
        print(f"成功批次: {successful_batches}/{total_batches}")
        print(f"总数据点: {total_points:,}")
        print(f"文件位置: {output_file}")
        
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / 1024 / 1024
            print(f"文件大小: {file_size:.2f} MB")
            
            # 验证数据
            try:
                df = pd.read_csv(output_file)
                print(f"文件验证: ✅ 共 {len(df):,} 行数据")
                
                if len(df) > 0:
                    print(f"\n📊 数据统计:")
                    print(f"时间跨度: {df['date'].iloc[0]} 到 {df['date'].iloc[-1]}")
                    print(f"平均距离: {df['distance_au'].mean():.4f} AU")
                    print(f"距离范围: {df['distance_au'].min():.4f} - {df['distance_au'].max():.4f} AU")
                    print(f"平均速度: {df['speed_km_s'].mean():.2f} km/s")
                    print(f"速度范围: {df['speed_km_s'].min():.2f} - {df['speed_km_s'].max():.2f} km/s")
                    print(f"平均光时: {df['light_time_sec'].mean()/60:.1f} 分钟")
                    
                    # 计算实际时间间隔
                    if len(df) > 1:
                        time_diffs = df['julian_date'].diff().dropna() * 24  # 小时
                        avg_interval = time_diffs.mean()
                        print(f"实际时间间隔: {avg_interval:.4f} 小时")
                        print(f"目标木星日: {self.jupiter_params['rotation_period_hours']:.4f} 小时")
                        print(f"间隔误差: {abs(avg_interval - self.jupiter_params['rotation_period_hours']):.4f} 小时")
                    
            except Exception as e:
                print(f"文件验证失败: {e}")
        
        print(f"\n✅ 木星完整公转周期数据下载成功！")
        print(f"数据覆盖: {self.start_date.strftime('%Y-%m-%d')} 到 {self.end_date.strftime('%Y-%m-%d')}")
        print(f"时间间隔: 接近木星日 ({self.jupiter_params['step_minutes']/60:.2f}小时)")

def main():
    """主函数"""
    downloader = JupiterFinalDownloader()
    
    print(f"\n⚠️ 注意事项:")
    print("- 完整下载预计需要2-3小时")
    print("- 数据量约10,000+个数据点")
    print("- 文件大小约1-2MB")
    print("- 使用596分钟间隔（最接近木星日9.925小时）")
    
    print(f"\n开始下载？")
    # 自动开始（实际使用时可以取消注释确认）
    # choice = input("继续？(y/n): ").lower().strip()
    # if choice == 'y' or choice == 'yes':
    print("y  # 自动开始")
    downloader.download_in_batches()
    # else:
    #     print("下载已取消")

if __name__ == "__main__":
    main()
