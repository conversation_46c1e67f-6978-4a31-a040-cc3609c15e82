# 木星公转周期数据下载方案

## 📋 基本信息

### 木星轨道参数
- **公转周期**: 11.862 年 (约 4,332.59 天)
- **平均距离**: 5.204 AU
- **轨道偏心率**: 0.0489
- **轨道倾角**: 1.304°

### 木星日（自转周期）
- **木星日长度**: 9小时55分30秒 ≈ 9.925 小时 ≈ 0.4135 地球日
- **每个地球日的木星日数**: 1 ÷ 0.4135 ≈ 2.42 个木星日

## 🎯 下载方案选项

### 方案一：完整公转周期 + 地球日间隔
```
时间范围: 2023-01-01 到 2034-11-26 (约11.86年)
时间间隔: 1天 (地球日)
数据点数: 4,333 个
文件大小: 约 600 KB
优点: 覆盖完整公转周期，数据连续
缺点: 数据量大，下载时间长
```

### 方案二：完整公转周期 + 木星日间隔 ⭐ **推荐**
```
时间范围: 2023-01-01 到 2034-11-26 (约11.86年)
时间间隔: 0.4135天 (约9小时55分)
数据点数: 10,480 个
文件大小: 约 1.4 MB
优点: 按木星自转周期采样，更符合木星时间
缺点: 数据量更大
```

### 方案三：一年木星轨道 + 木星日间隔
```
时间范围: 2023-01-01 到 2024-01-01 (1年)
时间间隔: 0.4135天 (约9小时55分)
数据点数: 883 个
文件大小: 约 120 KB
优点: 数据量适中，快速验证
缺点: 只覆盖部分轨道
```

### 方案四：半个公转周期 + 地球日间隔
```
时间范围: 2023-01-01 到 2029-01-01 (约6年)
时间间隔: 1天
数据点数: 2,191 个
文件大小: 约 300 KB
优点: 覆盖半个轨道，数据量适中
缺点: 不是完整周期
```

## 🔧 技术实现细节

### 时间间隔计算
```python
# 木星日长度（小时）
jupiter_day_hours = 9 + 55/60 + 30/3600  # 9.925 小时

# 转换为地球日
jupiter_day_earth_days = jupiter_day_hours / 24  # 0.4135 地球日

# JPL HORIZONS 时间步长格式
step_size = f"{jupiter_day_earth_days:.4f}d"  # "0.4135d"
```

### 下载参数配置
```python
# 木星 JPL HORIZONS ID
jupiter_id = '599'

# 时间范围（完整公转周期）
start_date = '2023-01-01'
end_date = '2034-11-26'  # 11.862年后

# 时间步长选项
step_options = {
    '地球日': '1d',
    '木星日': '0.4135d',
    '半木星日': '0.2068d',
    '6小时': '0.25d'
}
```

### 数据列结构
```
- julian_date: 儒略日
- date: 日期时间
- x_position_au: X位置 (AU)
- y_position_au: Y位置 (AU) 
- z_position_au: Z位置 (AU)
- x_velocity_km_s: X速度 (km/s)
- y_velocity_km_s: Y速度 (km/s)
- z_velocity_km_s: Z速度 (km/s)
- distance_au: 距离 (AU)
- distance_km: 距离 (km)
- light_time_sec: 光时 (秒)
- range_rate_km_s: 距离变化率 (km/s)
- speed_km_s: 轨道速度 (km/s)
```

## 📊 预期数据特征

### 轨道速度变化
- **近日点速度**: ~13.7 km/s
- **远日点速度**: ~12.4 km/s
- **平均速度**: ~13.1 km/s

### 距离变化
- **近日点距离**: ~4.95 AU
- **远日点距离**: ~5.46 AU
- **平均距离**: ~5.20 AU

### 光时变化
- **最短光时**: ~41 分钟
- **最长光时**: ~45 分钟
- **平均光时**: ~43 分钟

## 🚀 推荐执行步骤

### 第一步：测试下载（方案三）
1. 下载一年的木星数据验证连接
2. 检查数据质量和格式
3. 验证计算结果

### 第二步：完整下载（方案二）
1. 下载完整公转周期数据
2. 按木星日间隔采样
3. 生成完整的轨道分析数据

### 第三步：数据处理
1. 计算轨道参数变化
2. 分析速度和距离周期性
3. 生成可视化图表

## 💾 文件命名规范
```
木星_完整公转周期_木星日间隔_轨道数据.csv
木星_2023年_木星日间隔_轨道数据.csv
木星_半周期_地球日间隔_轨道数据.csv
```

## ⚠️ 注意事项

1. **下载时间**: 完整周期数据下载可能需要10-30分钟
2. **网络稳定性**: 确保VPN连接稳定
3. **存储空间**: 预留至少5MB空间
4. **数据验证**: 下载后检查数据完整性
5. **备份**: 重要数据及时备份

## 🎯 建议选择

**推荐方案二**：完整公转周期 + 木星日间隔
- 科学意义最大
- 数据最完整
- 符合木星时间尺度
- 适合深入研究

是否开始执行下载？请确认选择的方案。
