#!/usr/bin/env python3
"""
测试严格时间序列下载器 - 只下载前几个批次
"""

import os
import json
import pandas as pd
import warnings
import ssl
import urllib3

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

def test_precise_download():
    """测试精确下载"""
    print("🧪 测试严格时间序列下载")
    print("=" * 60)
    
    # 加载批次信息
    try:
        with open('jupiter_download_batches.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        batches = data['batches']
        print(f"✅ 加载了 {len(batches)} 个批次")
    except FileNotFoundError:
        print("❌ 批次文件不存在，请先运行时间序列生成器")
        return
    
    # 只测试前3个批次
    test_batches = batches[:3]
    print(f"🔬 测试前 {len(test_batches)} 个批次")
    
    results = []
    
    for i, batch in enumerate(test_batches, 1):
        batch_id = batch['batch_id']
        start_date = batch['jpl_start_date']
        end_date = batch['jpl_end_date']
        expected_points = batch['time_points']
        
        print(f"\n📦 测试批次 {i}/{len(test_batches)}")
        print(f"   批次ID: {batch_id}")
        print(f"   时间范围: {start_date} 到 {end_date}")
        print(f"   期望点数: {expected_points}")
        
        try:
            from astroquery.jplhorizons import Horizons
            
            print(f"   正在查询JPL HORIZONS...")
            
            jupiter = Horizons(
                id='599',
                location='@sun',
                epochs={
                    'start': start_date,
                    'stop': end_date,
                    'step': '596m'
                }
            )
            
            vectors = jupiter.vectors()
            df = vectors.to_pandas()
            
            if len(df) > 0:
                print(f"   ✅ 成功获取 {len(df)} 个数据点")
                
                # 简单分析时间间隔
                df['datetime'] = pd.to_datetime(df['datetime_str'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
                df = df.sort_values('datetime')
                
                if len(df) > 1:
                    time_diffs = df['datetime'].diff().dropna()
                    avg_interval_hours = time_diffs.dt.total_seconds().mean() / 3600
                    print(f"   平均间隔: {avg_interval_hours:.4f} 小时")
                    print(f"   与木星日差异: {abs(avg_interval_hours - 9.925):.4f} 小时")
                
                results.append({
                    'batch_id': batch_id,
                    'status': 'success',
                    'points': len(df),
                    'expected': expected_points,
                    'start_time': df['datetime'].iloc[0].isoformat(),
                    'end_time': df['datetime'].iloc[-1].isoformat()
                })
                
            else:
                print(f"   ⚠️ 无数据返回")
                results.append({
                    'batch_id': batch_id,
                    'status': 'no_data',
                    'points': 0,
                    'expected': expected_points
                })
                
        except Exception as e:
            print(f"   ❌ 下载失败: {e}")
            results.append({
                'batch_id': batch_id,
                'status': 'failed',
                'points': 0,
                'expected': expected_points,
                'error': str(e)
            })
        
        print(f"   ⏳ 等待2秒...")
        import time
        time.sleep(2)
    
    # 总结测试结果
    print(f"\n📊 测试结果总结:")
    print("=" * 60)
    
    successful = [r for r in results if r['status'] == 'success']
    failed = [r for r in results if r['status'] != 'success']
    
    print(f"成功批次: {len(successful)}/{len(test_batches)}")
    print(f"失败批次: {len(failed)}")
    
    if successful:
        total_points = sum(r['points'] for r in successful)
        total_expected = sum(r['expected'] for r in successful)
        print(f"总下载点数: {total_points}")
        print(f"总期望点数: {total_expected}")
        print(f"完成率: {total_points/total_expected*100:.2f}%")
        
        print(f"\n✅ 测试成功！严格时间序列下载器工作正常")
        print("可以继续进行完整下载")
    else:
        print(f"\n❌ 测试失败！需要检查网络连接和配置")
    
    # 保存测试结果
    with open('test_download_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"测试结果已保存: test_download_results.json")

def check_time_sequence_quality():
    """检查时间序列质量"""
    print(f"\n🔍 检查时间序列质量")
    print("=" * 60)
    
    try:
        with open('jupiter_time_sequence.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        metadata = data['metadata']
        time_sequence = data['time_sequence']
        
        print(f"📊 时间序列信息:")
        print(f"   总时间点: {len(time_sequence):,}")
        print(f"   木星日长度: {metadata['jupiter_day_hours']} 小时")
        print(f"   时间范围: {metadata['start_time']} 到 {metadata['end_time']}")
        
        # 检查前几个时间点
        print(f"\n📋 前10个时间点:")
        for i in range(min(10, len(time_sequence))):
            point = time_sequence[i]
            print(f"   {i+1:2d}. {point['datetime'][:19]} (索引 {point['index']})")
        
        # 检查时间间隔
        if len(time_sequence) >= 2:
            from datetime import datetime
            
            intervals = []
            for i in range(1, min(100, len(time_sequence))):
                prev_time = datetime.fromisoformat(time_sequence[i-1]['datetime'])
                curr_time = datetime.fromisoformat(time_sequence[i]['datetime'])
                interval = (curr_time - prev_time).total_seconds() / 3600
                intervals.append(interval)
            
            import numpy as np
            intervals = np.array(intervals)
            
            print(f"\n⏰ 时间间隔检查 (前99个间隔):")
            print(f"   平均间隔: {intervals.mean():.6f} 小时")
            print(f"   标准差: {intervals.std():.6f} 小时")
            print(f"   目标间隔: {metadata['jupiter_day_hours']} 小时")
            print(f"   最大偏差: {abs(intervals - metadata['jupiter_day_hours']).max():.6f} 小时")
            
            if intervals.std() < 0.001:
                print(f"   ✅ 时间序列完美！")
            else:
                print(f"   ⚠️ 时间序列存在偏差")
        
    except FileNotFoundError:
        print("❌ 时间序列文件不存在")

def main():
    """主函数"""
    print("🪐 严格时间序列下载器测试")
    print("=" * 60)
    
    # 1. 检查时间序列质量
    check_time_sequence_quality()
    
    # 2. 测试下载
    test_precise_download()

if __name__ == "__main__":
    main()
