#!/usr/bin/env python3
"""
实用精确下载方案
基于JPL系统限制的现实解决方案
"""

import json
import pandas as pd
from datetime import datetime, timedelta
import numpy as np

class PracticalPreciseDownloader:
    def __init__(self):
        """初始化实用精确下载方案"""
        self.jupiter_day_minutes = 595.5  # 精确木星日
        self.jpl_595m_error = 595 - 595.5  # -0.5分钟
        self.jpl_596m_error = 596 - 595.5  # +0.5分钟
        
        # 木星公转周期
        self.jupiter_orbital_period_days = 4332.589
        self.end_time = datetime(2024, 12, 31, 0, 0, 0)
        self.start_time = self.end_time - timedelta(days=self.jupiter_orbital_period_days)
        
        print(f"🛠️ 实用精确下载方案")
        print(f"精确木星日: {self.jupiter_day_minutes} 分钟")
        print(f"JPL 595m误差: {self.jpl_595m_error:.1f} 分钟")
        print(f"JPL 596m误差: {self.jpl_596m_error:.1f} 分钟")
    
    def design_practical_strategy(self):
        """设计实用策略"""
        print(f"\n🎯 设计实用下载策略")
        print("=" * 60)
        
        total_intervals = int(self.jupiter_orbital_period_days * 24 * 60 / self.jupiter_day_minutes)
        print(f"总时间间隔数: {total_intervals:,}")
        
        # 策略1：主要使用595m，定期使用596m补偿
        # 计算需要多少个596m来平衡595m的负误差
        
        # 设 x 个595m间隔，y 个596m间隔
        # x + y = total_intervals
        # -0.5*x + 0.5*y = 0 (总误差为0)
        # 解得：x = y，即各占一半
        
        intervals_595m = total_intervals // 2
        intervals_596m = total_intervals - intervals_595m
        
        total_error = self.jpl_595m_error * intervals_595m + self.jpl_596m_error * intervals_596m
        
        print(f"理论最优分配:")
        print(f"  595分钟间隔: {intervals_595m:,} 个")
        print(f"  596分钟间隔: {intervals_596m:,} 个")
        print(f"  理论总误差: {total_error:.6f} 分钟")
        
        # 策略2：实用分配 - 考虑JPL批次限制
        # 每个批次只能使用一种步长，所以按批次分配
        
        # 假设每批次约72个间隔（3天）
        intervals_per_batch = 72
        total_batches = (total_intervals + intervals_per_batch - 1) // intervals_per_batch
        
        batches_595m = total_batches // 2
        batches_596m = total_batches - batches_595m
        
        print(f"\n实用批次分配:")
        print(f"  总批次数: {total_batches}")
        print(f"  595m批次: {batches_595m}")
        print(f"  596m批次: {batches_596m}")
        print(f"  批次比例: {batches_595m/total_batches:.1%} : {batches_596m/total_batches:.1%}")
        
        return {
            'total_batches': total_batches,
            'batches_595m': batches_595m,
            'batches_596m': batches_596m,
            'intervals_per_batch': intervals_per_batch
        }
    
    def create_practical_batches(self, strategy):
        """创建实用批次"""
        print(f"\n📦 创建实用下载批次")
        print("=" * 60)
        
        batches = []
        current_time = self.start_time
        
        # 创建交替模式：595m和596m批次交替
        for batch_id in range(1, strategy['total_batches'] + 1):
            # 决定这个批次使用哪种步长
            if batch_id <= strategy['batches_595m']:
                # 前一半使用595m
                if (batch_id - 1) % 2 == 0:
                    step_minutes = 595
                else:
                    step_minutes = 596
            else:
                # 后一半使用596m
                step_minutes = 596
            
            # 计算批次的时间范围
            batch_start = current_time
            
            # 计算这个批次的结束时间
            intervals_in_batch = strategy['intervals_per_batch']
            if batch_id == strategy['total_batches']:
                # 最后一个批次可能不满
                remaining_time = self.end_time - current_time
                intervals_in_batch = int(remaining_time.total_seconds() / (step_minutes * 60))
            
            batch_end = batch_start + timedelta(minutes=step_minutes * intervals_in_batch)
            
            batch_info = {
                'batch_id': batch_id,
                'step_minutes': step_minutes,
                'jpl_step': f'{step_minutes}m',
                'start_time': batch_start.isoformat(),
                'end_time': batch_end.isoformat(),
                'jpl_start_date': batch_start.strftime('%Y-%m-%d'),
                'jpl_end_date': batch_end.strftime('%Y-%m-%d'),
                'expected_intervals': intervals_in_batch,
                'expected_points': intervals_in_batch + 1,
                'time_span_days': (batch_end - batch_start).total_seconds() / (24 * 3600)
            }
            
            batches.append(batch_info)
            current_time = batch_end
        
        # 统计
        batches_595 = [b for b in batches if b['step_minutes'] == 595]
        batches_596 = [b for b in batches if b['step_minutes'] == 596]
        
        print(f"✅ 实用批次创建完成")
        print(f"总批次数: {len(batches)}")
        print(f"595m批次: {len(batches_595)} ({len(batches_595)/len(batches)*100:.1f}%)")
        print(f"596m批次: {len(batches_596)} ({len(batches_596)/len(batches)*100:.1f}%)")
        
        # 计算预期误差
        total_intervals_595 = sum(b['expected_intervals'] for b in batches_595)
        total_intervals_596 = sum(b['expected_intervals'] for b in batches_596)
        expected_error = total_intervals_595 * self.jpl_595m_error + total_intervals_596 * self.jpl_596m_error
        
        print(f"\n预期误差分析:")
        print(f"  595m间隔总数: {total_intervals_595:,}")
        print(f"  596m间隔总数: {total_intervals_596:,}")
        print(f"  预期总误差: {expected_error:.3f} 分钟")
        print(f"  平均误差: {expected_error/(total_intervals_595+total_intervals_596):.6f} 分钟/间隔")
        
        # 显示前几个批次
        print(f"\n📋 批次预览:")
        for i, batch in enumerate(batches[:10]):
            print(f"  批次 {batch['batch_id']}: {batch['jpl_step']} | {batch['start_time'][:19]} 到 {batch['end_time'][:19]}")
        
        if len(batches) > 10:
            print(f"  ... 还有 {len(batches) - 10} 个批次")
        
        return batches
    
    def save_practical_plan(self, batches):
        """保存实用下载计划"""
        print(f"\n💾 保存实用下载计划")
        print("=" * 60)
        
        # 保存批次计划
        plan_file = 'jupiter_practical_download_plan.json'
        with open(plan_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'strategy': 'practical_mixed_batches',
                    'jupiter_day_minutes': self.jupiter_day_minutes,
                    'total_batches': len(batches),
                    'batches_595m': len([b for b in batches if b['step_minutes'] == 595]),
                    'batches_596m': len([b for b in batches if b['step_minutes'] == 596]),
                    'expected_total_points': sum(b['expected_points'] for b in batches),
                    'creation_time': datetime.now().isoformat(),
                    'version': 'practical_v1.0'
                },
                'batches': batches
            }, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 实用下载计划已保存: {plan_file}")
        print(f"   总批次数: {len(batches)}")
        print(f"   预期数据点: {sum(b['expected_points'] for b in batches):,}")
        
        return plan_file
    
    def create_practical_downloader(self, plan_file):
        """创建实用下载器脚本"""
        downloader_script = '''#!/usr/bin/env python3
"""
实用精确下载器 - 基于595/596分钟批次混合策略
"""

import os
import json
import pandas as pd
import numpy as np
import warnings
import ssl
import urllib3
import time
from datetime import datetime

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

def download_practical_batch(batch):
    """下载实用批次"""
    batch_id = batch['batch_id']
    jpl_step = batch['jpl_step']
    start_date = batch['jpl_start_date']
    end_date = batch['jpl_end_date']
    expected_points = batch['expected_points']
    
    print(f"批次 {batch_id}: {jpl_step} | {start_date} 到 {end_date} (期望 {expected_points} 点)")
    
    try:
        from astroquery.jplhorizons import Horizons
        
        jupiter = Horizons(
            id='599',
            location='@sun',
            epochs={
                'start': start_date,
                'stop': end_date,
                'step': jpl_step
            }
        )
        
        vectors = jupiter.vectors()
        df = vectors.to_pandas()
        
        if len(df) > 0:
            # 处理数据
            processed_df = process_data(df)
            
            # 保存批次文件
            batch_file = f"planet_data_practical/practical_batch_{batch_id:03d}.csv"
            os.makedirs('planet_data_practical', exist_ok=True)
            processed_df.to_csv(batch_file, index=False, encoding='utf-8-sig')
            
            print(f"   ✅ 成功: {len(processed_df)} 个点")
            
            return {
                'batch_id': batch_id,
                'status': 'success',
                'points': len(processed_df),
                'expected': expected_points,
                'step': jpl_step,
                'file': batch_file
            }
        else:
            print(f"   ⚠️ 无数据")
            return {
                'batch_id': batch_id,
                'status': 'no_data',
                'points': 0,
                'expected': expected_points,
                'step': jpl_step
            }
            
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return {
            'batch_id': batch_id,
            'status': 'failed',
            'points': 0,
            'expected': expected_points,
            'step': jpl_step,
            'error': str(e)
        }

def process_data(df):
    """处理数据"""
    au_to_km = 149597870.7
    day_to_sec = 86400
    light_speed_au_per_sec = 1.0 / 499.004783836
    
    processed_df = pd.DataFrame()
    
    processed_df['julian_date'] = df['datetime_jd']
    processed_df['date'] = df['datetime_str']
    processed_df['x_position_au'] = df['x']
    processed_df['y_position_au'] = df['y']
    processed_df['z_position_au'] = df['z']
    
    processed_df['x_velocity_km_s'] = df['vx'] * au_to_km / day_to_sec
    processed_df['y_velocity_km_s'] = df['vy'] * au_to_km / day_to_sec
    processed_df['z_velocity_km_s'] = df['vz'] * au_to_km / day_to_sec
    
    processed_df['distance_au'] = df['range']
    processed_df['distance_km'] = df['range'] * au_to_km
    processed_df['light_time_sec'] = df['range'] / light_speed_au_per_sec
    processed_df['range_rate_km_s'] = df['range_rate'] * au_to_km / day_to_sec
    
    processed_df['speed_km_s'] = np.sqrt(
        processed_df['x_velocity_km_s']**2 + 
        processed_df['y_velocity_km_s']**2 + 
        processed_df['z_velocity_km_s']**2
    )
    
    return processed_df

def main():
    """主函数"""
    print("🛠️ 实用精确木星数据下载器")
    print("=" * 60)
    
    # 加载下载计划
    with open('jupiter_practical_download_plan.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    batches = data['batches']
    print(f"加载了 {len(batches)} 个批次")
    
    # 执行下载
    results = []
    successful = 0
    total_points = 0
    
    for i, batch in enumerate(batches, 1):
        print(f"\\n进度: {i}/{len(batches)}")
        
        result = download_practical_batch(batch)
        results.append(result)
        
        if result['status'] == 'success':
            successful += 1
            total_points += result['points']
        
        # 批次间延迟
        if i < len(batches):
            time.sleep(2)
    
    # 保存结果
    with open('practical_download_results.json', 'w', encoding='utf-8') as f:
        json.dump({
            'summary': {
                'total_batches': len(batches),
                'successful_batches': successful,
                'total_points': total_points,
                'download_time': datetime.now().isoformat()
            },
            'results': results
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\\n📊 下载完成:")
    print(f"   成功批次: {successful}/{len(batches)}")
    print(f"   总数据点: {total_points:,}")

if __name__ == "__main__":
    main()
'''
        
        with open('实用精确下载器.py', 'w', encoding='utf-8') as f:
            f.write(downloader_script)
        
        print(f"✅ 实用下载器脚本已创建: 实用精确下载器.py")
    
    def generate_practical_solution(self):
        """生成实用解决方案"""
        print(f"🛠️ 生成实用精确下载解决方案")
        print("=" * 60)
        print("特点：")
        print("1. 基于JPL系统实际限制")
        print("2. 595/596分钟批次级混合")
        print("3. 最小化累积误差")
        print("4. 实际可执行")
        
        # 1. 设计策略
        strategy = self.design_practical_strategy()
        
        # 2. 创建批次
        batches = self.create_practical_batches(strategy)
        
        # 3. 保存计划
        plan_file = self.save_practical_plan(batches)
        
        # 4. 创建下载器
        self.create_practical_downloader(plan_file)
        
        print(f"\n🎉 实用精确下载解决方案完成！")
        print(f"下载计划: {plan_file}")
        print(f"下载器: 实用精确下载器.py")
        print(f"预期效果: 显著减少累积误差，提高覆盖率")
        
        return plan_file

def main():
    """主函数"""
    print("🛠️ 实用精确下载方案生成器")
    print("=" * 60)
    print("解决方案：")
    print("1. 承认JPL系统的单步长限制")
    print("2. 在批次级别实现595/596分钟混合")
    print("3. 通过交替批次策略平衡累积误差")
    print("4. 生成实际可执行的下载计划")
    
    generator = PracticalPreciseDownloader()
    plan_file = generator.generate_practical_solution()
    
    if plan_file:
        print(f"\n✅ 实用方案准备完成！")
        print("下一步：运行 实用精确下载器.py 执行下载")
        print("预期结果：更高的覆盖率和更小的累积误差")

if __name__ == "__main__":
    main()
