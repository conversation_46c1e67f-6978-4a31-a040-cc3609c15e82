#!/usr/bin/env python3
"""
专门测试地球数据下载
使用代理配置测试JPL HORIZONS地球数据获取
"""

import os
import pandas as pd
import numpy as np
import warnings
import ssl
import urllib3

# 忽略警告
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 处理SSL问题
ssl._create_default_https_context = ssl._create_unverified_context

def configure_proxy():
    """配置代理设置"""
    print("配置代理设置...")
    
    # 设置环境变量
    os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
    os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'
    
    print("代理配置完成: 127.0.0.1:7890")

def test_earth_data():
    """测试地球数据下载"""
    print("测试地球2023年数据下载...")
    print("=" * 50)
    
    try:
        from astroquery.jplhorizons import Horizons
        
        print("正在创建地球查询对象...")
        
        # 创建地球查询对象 - 测试2023年1月的数据
        earth = Horizons(
            id='399',  # 地球
            location='@sun',  # 日心坐标系
            epochs={'start': '2023-01-01', 'stop': '2023-01-31', 'step': '1d'}
        )
        
        print("正在查询JPL HORIZONS系统...")
        vectors = earth.vectors()
        
        print("✅ 数据获取成功！")
        print(f"获取到 {len(vectors)} 天的数据")
        
        # 转换为DataFrame
        df = vectors.to_pandas()
        
        # 显示数据信息
        print(f"\n数据列: {list(df.columns)}")
        print(f"数据形状: {df.shape}")
        
        # 选择和重命名列
        columns_mapping = {
            'datetime_jd': '儒略日',
            'datetime_str': '日期',
            'x': 'X位置_AU',
            'y': 'Y位置_AU', 
            'z': 'Z位置_AU',
            'vx': 'X速度_AU每天',
            'vy': 'Y速度_AU每天',
            'vz': 'Z速度_AU每天',
            'range': '距离_AU',
            'range_rate': '距离变化率_AU每天'
        }
        
        # 选择需要的列并重命名
        available_columns = [col for col in columns_mapping.keys() if col in df.columns]
        df_selected = df[available_columns].copy()
        df_selected.rename(columns=columns_mapping, inplace=True)
        
        # 计算轨道速度（km/s）
        if all(col in df.columns for col in ['vx', 'vy', 'vz']):
            # 1 AU/day = 1731.46 km/s
            au_day_to_km_s = 1731.46
            df_selected['轨道速度_km每秒'] = np.sqrt(
                df['vx']**2 + df['vy']**2 + df['vz']**2
            ) * au_day_to_km_s
        
        # 保存数据
        output_file = '地球_2023年1月_轨道数据.csv'
        df_selected.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n数据已保存到: {output_file}")
        
        # 显示数据摘要
        print(f"\n📊 数据摘要:")
        print(f"总天数: {len(df_selected)}")
        
        if '距离_AU' in df_selected.columns:
            print(f"平均距离: {df_selected['距离_AU'].mean():.6f} AU")
            print(f"最近距离: {df_selected['距离_AU'].min():.6f} AU")
            print(f"最远距离: {df_selected['距离_AU'].max():.6f} AU")
        
        if '轨道速度_km每秒' in df_selected.columns:
            print(f"平均速度: {df_selected['轨道速度_km每秒'].mean():.2f} km/s")
            print(f"最高速度: {df_selected['轨道速度_km每秒'].max():.2f} km/s")
            print(f"最低速度: {df_selected['轨道速度_km每秒'].min():.2f} km/s")
        
        # 显示前几行数据
        print(f"\n📋 前5行数据:")
        print(df_selected.head())
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请安装astroquery库: pip install astroquery")
        return False
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        return False

def test_full_year():
    """测试全年数据下载"""
    print("\n测试地球2023年全年数据下载...")
    print("=" * 50)
    
    try:
        from astroquery.jplhorizons import Horizons
        
        print("正在创建地球全年查询对象...")
        
        # 创建地球查询对象 - 2023年全年
        earth = Horizons(
            id='399',  # 地球
            location='@sun',  # 日心坐标系
            epochs={'start': '2023-01-01', 'stop': '2023-12-31', 'step': '1d'}
        )
        
        print("正在查询JPL HORIZONS系统（全年数据，请稍候）...")
        vectors = earth.vectors()
        
        print("✅ 全年数据获取成功！")
        print(f"获取到 {len(vectors)} 天的数据")
        
        # 转换为DataFrame并保存
        df = vectors.to_pandas()
        
        # 保存原始数据
        output_file = '地球_2023年_完整轨道数据.csv'
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"完整数据已保存到: {output_file}")
        
        # 显示统计信息
        print(f"\n📊 全年数据统计:")
        print(f"总天数: {len(df)}")
        print(f"数据列数: {len(df.columns)}")
        print(f"文件大小: {os.path.getsize(output_file) / 1024:.1f} KB")
        
        return True
        
    except Exception as e:
        print(f"❌ 全年数据下载失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🌍 JPL HORIZONS 地球轨道数据测试")
    print("=" * 60)
    
    # 配置代理
    configure_proxy()
    
    # 测试1：一个月数据
    print("\n🔍 测试1: 地球2023年1月数据")
    success1 = test_earth_data()
    
    if success1:
        print("\n✅ 月度数据测试成功！")
        
        # 询问是否继续测试全年数据
        print("\n🔍 测试2: 地球2023年全年数据")
        print("注意：全年数据下载可能需要较长时间...")
        
        success2 = test_full_year()
        
        if success2:
            print("\n🎉 所有测试完成！地球轨道数据下载功能正常。")
            print("\n📁 生成的文件:")
            print("- 地球_2023年1月_轨道数据.csv (月度数据)")
            print("- 地球_2023年_完整轨道数据.csv (全年数据)")
        else:
            print("\n⚠️  月度数据成功，但全年数据失败。")
            print("可能是网络超时，可以稍后重试全年数据。")
    else:
        print("\n❌ 地球数据下载测试失败")
        print("请检查:")
        print("1. VPN连接是否正常")
        print("2. 代理端口7890是否可用")
        print("3. 防火墙设置")

if __name__ == "__main__":
    main()
