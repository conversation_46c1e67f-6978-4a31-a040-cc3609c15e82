#!/usr/bin/env python3
"""
分析木星数据的覆盖率和时间分布
"""

import pandas as pd
import numpy as np
from datetime import datetime

def analyze_coverage():
    """分析数据覆盖率"""
    print("📊 木星数据覆盖率详细分析")
    print("=" * 60)
    
    # 读取数据
    df = pd.read_csv('planet_data/木星_完整公转周期_596分钟间隔_轨道数据.csv')
    
    # 转换日期
    df['datetime'] = pd.to_datetime(df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
    
    print(f"📅 时间范围分析:")
    start_time = df['datetime'].iloc[0]
    end_time = df['datetime'].iloc[-1]
    total_duration = (end_time - start_time).total_seconds() / (24 * 3600)  # 转换为天
    
    print(f"  开始时间: {start_time}")
    print(f"  结束时间: {end_time}")
    print(f"  总时长: {total_duration:.1f} 天")
    print(f"  总时长: {total_duration/365.25:.2f} 年")
    
    # 计算时间间隔
    time_diffs = df['datetime'].diff().dropna()
    time_diffs_hours = time_diffs.dt.total_seconds() / 3600
    
    print(f"\n⏰ 时间间隔分析:")
    print(f"  平均间隔: {time_diffs_hours.mean():.4f} 小时")
    print(f"  标准差: {time_diffs_hours.std():.4f} 小时")
    print(f"  最小间隔: {time_diffs_hours.min():.4f} 小时")
    print(f"  最大间隔: {time_diffs_hours.max():.4f} 小时")
    print(f"  目标间隔: 9.9333 小时 (596分钟)")
    
    # 检查异常间隔
    target_interval = 596 / 60  # 596分钟转小时
    tolerance = 1.0  # 1小时容差
    
    normal_intervals = time_diffs_hours[(time_diffs_hours >= target_interval - tolerance) & 
                                       (time_diffs_hours <= target_interval + tolerance)]
    abnormal_intervals = time_diffs_hours[(time_diffs_hours < target_interval - tolerance) | 
                                         (time_diffs_hours > target_interval + tolerance)]
    
    print(f"\n🔍 间隔质量分析:")
    print(f"  正常间隔数: {len(normal_intervals)} ({len(normal_intervals)/len(time_diffs_hours)*100:.1f}%)")
    print(f"  异常间隔数: {len(abnormal_intervals)} ({len(abnormal_intervals)/len(time_diffs_hours)*100:.1f}%)")
    
    if len(abnormal_intervals) > 0:
        print(f"  异常间隔范围: {abnormal_intervals.min():.2f} - {abnormal_intervals.max():.2f} 小时")
    
    # 理论vs实际对比
    print(f"\n📈 覆盖率计算:")
    theoretical_points = total_duration * 24 / target_interval
    actual_points = len(df)
    coverage_rate = actual_points / theoretical_points * 100
    
    print(f"  理论数据点: {theoretical_points:.0f}")
    print(f"  实际数据点: {actual_points}")
    print(f"  覆盖率: {coverage_rate:.2f}%")
    
    # 按年份分析覆盖率
    print(f"\n📆 年度覆盖率分析:")
    df['year'] = df['datetime'].dt.year
    yearly_stats = df.groupby('year').size()
    
    for year in sorted(df['year'].unique()):
        year_data = df[df['year'] == year]
        if len(year_data) > 1:
            year_start = year_data['datetime'].iloc[0]
            year_end = year_data['datetime'].iloc[-1]
            year_duration = (year_end - year_start).total_seconds() / (24 * 3600)
            year_theoretical = year_duration * 24 / target_interval if year_duration > 0 else 0
            year_coverage = len(year_data) / year_theoretical * 100 if year_theoretical > 0 else 0
            
            print(f"  {year}年: {len(year_data):4d} 点 (覆盖率: {year_coverage:.1f}%)")
    
    # 数据缺失分析
    print(f"\n🕳️ 数据缺失分析:")
    large_gaps = time_diffs_hours[time_diffs_hours > target_interval * 2]  # 超过2倍正常间隔
    
    if len(large_gaps) > 0:
        print(f"  发现 {len(large_gaps)} 个较大间隙:")
        for i, gap in enumerate(large_gaps.head(5)):  # 显示前5个最大间隙
            gap_index = time_diffs_hours[time_diffs_hours == gap].index[0]
            gap_time = df.loc[gap_index, 'datetime']
            print(f"    间隙 {i+1}: {gap:.2f}小时 (约{gap_time.strftime('%Y-%m-%d')})")
    else:
        print(f"  ✅ 未发现显著的数据间隙")
    
    # 总结
    print(f"\n🎯 覆盖率总结:")
    if coverage_rate >= 95:
        quality = "优秀"
        emoji = "🌟"
    elif coverage_rate >= 90:
        quality = "良好"
        emoji = "✅"
    elif coverage_rate >= 85:
        quality = "可接受"
        emoji = "⚠️"
    else:
        quality = "需要改进"
        emoji = "❌"
    
    print(f"  数据质量: {quality} {emoji}")
    print(f"  覆盖率: {coverage_rate:.2f}%")
    print(f"  缺失数据: {100-coverage_rate:.2f}%")
    
    if coverage_rate >= 95:
        print(f"  ✅ 数据质量优秀，完全满足科学分析需求")
        print(f"  ✅ 可用于精确的轨道力学计算")
        print(f"  ✅ 适合长期轨道演化研究")
    
    return coverage_rate

if __name__ == "__main__":
    analyze_coverage()
