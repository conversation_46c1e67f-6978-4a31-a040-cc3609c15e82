#!/usr/bin/env python3
"""
找出木星数据中的缺失时间段，准备补充下载
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def find_missing_data():
    """找出缺失的数据时间段"""
    print("🔍 分析木星数据缺失时间段")
    print("=" * 60)
    
    # 读取现有数据
    df = pd.read_csv('planet_data/木星_完整公转周期_596分钟间隔_轨道数据.csv')
    
    # 转换日期格式
    df['datetime'] = pd.to_datetime(df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
    df = df.sort_values('datetime').reset_index(drop=True)
    
    print(f"📊 现有数据概况:")
    print(f"  数据点数: {len(df):,}")
    print(f"  时间范围: {df['datetime'].iloc[0]} 到 {df['datetime'].iloc[-1]}")
    
    # 计算时间间隔
    time_diffs = df['datetime'].diff().dropna()
    time_diffs_hours = time_diffs.dt.total_seconds() / 3600
    
    # 目标间隔：596分钟 = 9.9333小时
    target_interval_hours = 596 / 60
    tolerance = 2.0  # 2小时容差，超过这个认为是缺失
    
    print(f"\n⏰ 时间间隔分析:")
    print(f"  目标间隔: {target_interval_hours:.4f} 小时")
    print(f"  平均间隔: {time_diffs_hours.mean():.4f} 小时")
    print(f"  容差阈值: {target_interval_hours + tolerance:.1f} 小时")
    
    # 找出大间隙（可能的缺失数据）
    large_gaps = time_diffs_hours[time_diffs_hours > target_interval_hours + tolerance]
    
    print(f"\n🕳️ 发现的数据间隙:")
    print(f"  间隙总数: {len(large_gaps)}")
    
    missing_periods = []
    
    if len(large_gaps) > 0:
        for i, gap_hours in enumerate(large_gaps):
            gap_index = time_diffs_hours[time_diffs_hours == gap_hours].index[0]
            
            # 间隙前后的时间点
            time_before = df.loc[gap_index-1, 'datetime']
            time_after = df.loc[gap_index, 'datetime']
            
            # 计算应该有多少个数据点
            expected_points = int(gap_hours / target_interval_hours) - 1
            
            print(f"\n  间隙 {i+1}:")
            print(f"    时间: {time_before} 到 {time_after}")
            print(f"    间隔: {gap_hours:.2f} 小时")
            print(f"    缺失点数: 约 {expected_points} 个")
            
            if expected_points > 0:
                missing_periods.append({
                    'start': time_before + timedelta(hours=target_interval_hours),
                    'end': time_after - timedelta(hours=target_interval_hours),
                    'gap_hours': gap_hours,
                    'missing_points': expected_points
                })
    
    # 检查开始和结束时间是否需要扩展
    print(f"\n📅 时间范围检查:")
    
    # 理论开始时间：2024-12-31往前推4332.59天
    target_end = datetime(2024, 12, 31)
    target_start = target_end - timedelta(days=4332.59)
    
    actual_start = df['datetime'].iloc[0]
    actual_end = df['datetime'].iloc[-1]
    
    print(f"  目标范围: {target_start} 到 {target_end}")
    print(f"  实际范围: {actual_start} 到 {actual_end}")
    
    # 检查开始时间缺失
    if actual_start > target_start:
        start_gap_hours = (actual_start - target_start).total_seconds() / 3600
        start_missing_points = int(start_gap_hours / target_interval_hours)
        print(f"  开始时间缺失: {start_gap_hours:.1f} 小时 (约 {start_missing_points} 个点)")
        
        missing_periods.insert(0, {
            'start': target_start,
            'end': actual_start - timedelta(hours=target_interval_hours),
            'gap_hours': start_gap_hours,
            'missing_points': start_missing_points,
            'type': 'start_extension'
        })
    
    # 检查结束时间缺失
    if actual_end < target_end:
        end_gap_hours = (target_end - actual_end).total_seconds() / 3600
        end_missing_points = int(end_gap_hours / target_interval_hours)
        print(f"  结束时间缺失: {end_gap_hours:.1f} 小时 (约 {end_missing_points} 个点)")
        
        missing_periods.append({
            'start': actual_end + timedelta(hours=target_interval_hours),
            'end': target_end,
            'gap_hours': end_gap_hours,
            'missing_points': end_missing_points,
            'type': 'end_extension'
        })
    
    # 总结缺失数据
    total_missing_points = sum(period['missing_points'] for period in missing_periods)
    
    print(f"\n📋 缺失数据总结:")
    print(f"  缺失时间段数: {len(missing_periods)}")
    print(f"  总缺失点数: {total_missing_points}")
    print(f"  当前数据点: {len(df):,}")
    print(f"  补充后预计: {len(df) + total_missing_points:,}")
    
    # 生成下载计划
    print(f"\n📝 补充下载计划:")
    
    download_tasks = []
    for i, period in enumerate(missing_periods):
        # 将时间段分解为小批次（每批7天）
        batch_days = 7
        current_start = period['start']
        period_end = period['end']
        
        batch_num = 1
        while current_start < period_end:
            batch_end = min(current_start + timedelta(days=batch_days), period_end)
            
            download_tasks.append({
                'period_id': i + 1,
                'batch_id': batch_num,
                'start': current_start.strftime('%Y-%m-%d'),
                'end': batch_end.strftime('%Y-%m-%d'),
                'start_datetime': current_start,
                'end_datetime': batch_end
            })
            
            print(f"  任务 {len(download_tasks)}: {current_start.strftime('%Y-%m-%d')} 到 {batch_end.strftime('%Y-%m-%d')}")
            
            current_start = batch_end
            batch_num += 1
    
    print(f"\n🎯 执行计划:")
    print(f"  总下载任务: {len(download_tasks)} 个批次")
    print(f"  预计时间: {len(download_tasks) * 0.5:.1f} 分钟")
    print(f"  完成后覆盖率: 接近 100%")
    
    return download_tasks

if __name__ == "__main__":
    tasks = find_missing_data()
    
    # 保存任务列表
    if tasks:
        import json
        with open('missing_data_tasks.json', 'w', encoding='utf-8') as f:
            # 转换datetime为字符串以便JSON序列化
            tasks_json = []
            for task in tasks:
                task_json = task.copy()
                task_json['start_datetime'] = task['start_datetime'].isoformat()
                task_json['end_datetime'] = task['end_datetime'].isoformat()
                tasks_json.append(task_json)
            
            json.dump(tasks_json, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 下载任务已保存到: missing_data_tasks.json")
    else:
        print(f"\n✅ 未发现需要补充的数据！")
