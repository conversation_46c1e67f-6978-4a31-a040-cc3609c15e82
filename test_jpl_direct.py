#!/usr/bin/env python3
"""
直接测试JPL HORIZONS API连接
使用requests库直接访问JPL HORIZONS API
"""

import requests
import ssl
import urllib3
from urllib.parse import urlencode

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_jpl_connection():
    """测试JPL网站连接"""
    print("测试JPL HORIZONS网站连接...")
    print("=" * 50)
    
    try:
        # 测试基本连接
        url = "https://ssd.jpl.nasa.gov"
        print(f"正在连接: {url}")
        
        response = requests.get(url, timeout=30, verify=False)
        print(f"连接状态: {response.status_code}")
        
        if response.status_code == 200:
            print("JPL网站连接成功！")
            return True
        else:
            print(f"连接失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"连接失败: {e}")
        return False

def test_horizons_api():
    """测试JPL HORIZONS API"""
    print("\n测试JPL HORIZONS API...")
    print("=" * 50)
    
    try:
        # JPL HORIZONS API URL
        base_url = "https://ssd.jpl.nasa.gov/api/horizons.api"
        
        # 构建查询参数（获取地球7天数据）
        params = {
            'format': 'text',
            'COMMAND': '399',  # 地球
            'OBJ_DATA': 'YES',
            'MAKE_EPHEM': 'YES',
            'EPHEM_TYPE': 'VECTORS',
            'CENTER': '@sun',
            'START_TIME': '2023-01-01',
            'STOP_TIME': '2023-01-07',
            'STEP_SIZE': '1d',
            'OUT_UNITS': 'AU-D',
            'REF_PLANE': 'ECLIPTIC',
            'REF_SYSTEM': 'ICRF',
            'VEC_CORR': 'NONE',
            'VEC_LABELS': 'YES',
            'CSV_FORMAT': 'YES'
        }
        
        print("正在构建API请求...")
        print(f"查询参数: {params}")
        
        # 发送请求
        print("正在发送API请求...")
        response = requests.get(base_url, params=params, timeout=60, verify=False)
        
        print(f"API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print("API请求成功！")
            
            # 保存响应数据
            with open('jpl_response.txt', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("响应数据已保存到: jpl_response.txt")
            
            # 显示部分响应内容
            print("\n响应内容预览:")
            print("-" * 30)
            lines = response.text.split('\n')
            for i, line in enumerate(lines[:20]):  # 显示前20行
                print(f"{i+1:2d}: {line}")
            
            if len(lines) > 20:
                print(f"... (还有 {len(lines)-20} 行)")
            
            return True
        else:
            print(f"API请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"API请求失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        return False

def test_with_proxy():
    """测试使用代理的连接"""
    print("\n测试代理连接...")
    print("=" * 50)
    
    # 常见的本地代理端口
    proxy_ports = [1080, 7890, 8080, 10809]
    
    for port in proxy_ports:
        try:
            proxies = {
                'http': f'http://127.0.0.1:{port}',
                'https': f'http://127.0.0.1:{port}'
            }
            
            print(f"尝试代理端口: {port}")
            
            response = requests.get(
                'https://ssd.jpl.nasa.gov',
                proxies=proxies,
                timeout=10,
                verify=False
            )
            
            if response.status_code == 200:
                print(f"代理端口 {port} 连接成功！")
                return proxies
            else:
                print(f"代理端口 {port} 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"代理端口 {port} 失败: {e}")
    
    print("所有代理端口都无法连接")
    return None

def main():
    """主测试函数"""
    print("JPL HORIZONS 直接连接测试")
    print("=" * 60)
    
    # 测试1：基本连接
    basic_success = test_jpl_connection()
    
    if basic_success:
        # 测试2：API调用
        api_success = test_horizons_api()
        
        if api_success:
            print("\n✅ 所有测试通过！JPL HORIZONS连接正常。")
        else:
            print("\n❌ API调用失败，但基本连接正常。")
    else:
        print("\n基本连接失败，尝试代理连接...")
        
        # 测试3：代理连接
        proxy_config = test_with_proxy()
        
        if proxy_config:
            print(f"\n找到可用代理: {proxy_config}")
            print("请在astroquery配置中使用此代理设置")
        else:
            print("\n❌ 所有连接方式都失败了")
            print("\n故障排除建议:")
            print("1. 确认VPN已正确连接")
            print("2. 检查防火墙设置")
            print("3. 尝试在浏览器中访问: https://ssd.jpl.nasa.gov/horizons/")
            print("4. 检查代理软件的端口设置")

if __name__ == "__main__":
    main()
