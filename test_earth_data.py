#!/usr/bin/env python3
"""
测试地球数据下载脚本
专门用于测试JPL HORIZONS地球数据下载功能
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
import ssl
import urllib3

# 忽略警告
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 处理SSL问题
ssl._create_default_https_context = ssl._create_unverified_context

def test_earth_data_simple():
    """简单测试地球数据下载"""
    print("测试地球数据下载...")
    print("=" * 50)
    
    try:
        from astroquery.jplhorizons import Horizons
        from astroquery import conf
        
        # 配置超时时间
        conf.remote_timeout = 180
        
        print("正在连接JPL HORIZONS系统...")
        
        # 创建地球查询对象
        earth = Horizons(
            id='399',  # 地球
            location='@sun',  # 日心坐标系
            epochs={'start': '2023-01-01', 'stop': '2023-01-31', 'step': '1d'}  # 先测试一个月
        )
        
        print("正在获取地球轨道数据...")
        vectors = earth.vectors()
        
        print("数据获取成功！")
        print(f"获取到 {len(vectors)} 天的数据")
        
        # 转换为DataFrame
        df = vectors.to_pandas()
        print(f"数据列: {list(df.columns)}")
        
        # 保存数据
        output_file = 'earth_test_data.csv'
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {output_file}")
        
        # 显示前几行数据
        print("\n前5行数据:")
        print(df.head())
        
        return True
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请安装astroquery库: pip install astroquery")
        return False
        
    except Exception as e:
        print(f"下载失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 尝试诊断网络问题
        try:
            import requests
            print("\n测试网络连接...")
            response = requests.get('https://ssd.jpl.nasa.gov', timeout=10, verify=False)
            print(f"JPL网站连接状态: {response.status_code}")
        except Exception as net_e:
            print(f"网络连接测试失败: {net_e}")
        
        return False

def test_with_different_settings():
    """使用不同设置测试"""
    print("\n使用不同设置重新测试...")
    print("=" * 50)
    
    try:
        from astroquery.jplhorizons import Horizons
        
        # 尝试使用更短的时间范围
        print("测试更短时间范围（7天）...")
        
        earth = Horizons(
            id='399',
            location='@sun',
            epochs={'start': '2023-01-01', 'stop': '2023-01-07', 'step': '1d'}
        )
        
        vectors = earth.vectors()
        print("短时间范围测试成功！")
        
        # 保存数据
        df = vectors.to_pandas()
        output_file = 'earth_week_test.csv'
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"短时间范围测试也失败: {e}")
        return False

def main():
    """主测试函数"""
    print("JPL HORIZONS 地球数据下载测试")
    print("=" * 60)
    
    # 检查网络环境
    print("检查Python环境...")
    try:
        import astroquery
        print(f"astroquery版本: {astroquery.__version__}")
    except ImportError:
        print("astroquery未安装，请运行: pip install astroquery")
        return
    
    # 测试1：标准测试
    success1 = test_earth_data_simple()
    
    if not success1:
        # 测试2：备用测试
        success2 = test_with_different_settings()
        
        if not success2:
            print("\n所有测试都失败了。")
            print("可能的原因：")
            print("1. VPN连接不稳定")
            print("2. JPL HORIZONS服务器暂时不可用")
            print("3. 防火墙阻止了连接")
            print("4. 需要配置代理设置")
            
            print("\n建议：")
            print("1. 检查VPN连接")
            print("2. 稍后重试")
            print("3. 尝试直接访问JPL HORIZONS网站: https://ssd.jpl.nasa.gov/horizons/")
    else:
        print("\n测试成功！地球数据下载正常工作。")

if __name__ == "__main__":
    main()
