#!/usr/bin/env python3
"""
自动下载所有剩余批次
"""

import os
import json
import subprocess
import time
from datetime import datetime

def get_total_batches():
    """获取总批次数"""
    with open('jupiter_download_batches.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    return len(data['batches'])

def get_completed_batches():
    """获取已完成的批次"""
    completed = set()
    
    # 检查结果文件
    for file in os.listdir('.'):
        if file.startswith('download_results_') and file.endswith('.json'):
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for result in data['results']:
                    if result['status'] == 'success':
                        completed.add(result['batch_id'])
            except:
                continue
    
    return completed

def download_remaining_batches():
    """下载剩余的批次"""
    print("🪐 自动下载所有剩余批次")
    print("=" * 60)
    
    total_batches = get_total_batches()
    completed_batches = get_completed_batches()
    
    print(f"总批次数: {total_batches}")
    print(f"已完成: {len(completed_batches)}")
    print(f"剩余: {total_batches - len(completed_batches)}")
    
    if len(completed_batches) >= total_batches:
        print("✅ 所有批次已完成！")
        return
    
    # 确定下载范围
    segment_size = 20  # 每次下载20个批次
    
    # 找到下一个未完成的批次
    next_batch = 1
    while next_batch in completed_batches and next_batch <= total_batches:
        next_batch += 1
    
    print(f"从批次 {next_batch} 开始下载")
    
    # 分段下载
    current_batch = next_batch
    
    while current_batch <= total_batches:
        end_batch = min(current_batch + segment_size - 1, total_batches)
        
        print(f"\n🚀 下载段落: {current_batch} 到 {end_batch}")
        print(f"时间: {datetime.now().strftime('%H:%M:%S')}")
        
        try:
            # 调用分段下载器
            result = subprocess.run([
                'python', '分段下载器.py', str(current_batch), str(end_batch)
            ], capture_output=True, text=True, timeout=1200)  # 20分钟超时
            
            if result.returncode == 0:
                print(f"✅ 段落 {current_batch}-{end_batch} 下载成功")
            else:
                print(f"❌ 段落 {current_batch}-{end_batch} 下载失败")
                print(f"错误: {result.stderr}")
                
                # 记录失败的段落
                with open('failed_segments.txt', 'a', encoding='utf-8') as f:
                    f.write(f"{current_batch}-{end_batch}: {datetime.now()}\n")
            
        except subprocess.TimeoutExpired:
            print(f"⏰ 段落 {current_batch}-{end_batch} 超时")
            
            # 记录超时的段落
            with open('timeout_segments.txt', 'a', encoding='utf-8') as f:
                f.write(f"{current_batch}-{end_batch}: {datetime.now()}\n")
        
        except Exception as e:
            print(f"❌ 段落 {current_batch}-{end_batch} 异常: {e}")
        
        current_batch = end_batch + 1
        
        # 段落间休息
        if current_batch <= total_batches:
            print(f"⏳ 休息30秒...")
            time.sleep(30)
    
    print(f"\n🎉 所有批次下载完成！")

def merge_all_results():
    """合并所有下载结果"""
    print(f"\n🔄 合并所有下载结果")
    print("=" * 60)
    
    all_results = []
    total_points = 0
    successful_batches = 0
    
    # 收集所有结果文件
    result_files = [f for f in os.listdir('.') if f.startswith('download_results_') and f.endswith('.json')]
    result_files.sort()
    
    print(f"找到 {len(result_files)} 个结果文件")
    
    for file in result_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for result in data['results']:
                all_results.append(result)
                if result['status'] == 'success':
                    successful_batches += 1
                    total_points += result['points']
            
            print(f"   ✅ {file}: {data['successful_batches']} 成功批次")
            
        except Exception as e:
            print(f"   ❌ {file}: 读取失败 - {e}")
    
    # 保存合并结果
    final_results = {
        'summary': {
            'total_batches': len(all_results),
            'successful_batches': successful_batches,
            'failed_batches': len(all_results) - successful_batches,
            'total_points': total_points,
            'merge_time': datetime.now().isoformat()
        },
        'results': all_results
    }
    
    with open('final_download_results.json', 'w', encoding='utf-8') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 合并结果:")
    print(f"   总批次: {len(all_results)}")
    print(f"   成功批次: {successful_batches}")
    print(f"   失败批次: {len(all_results) - successful_batches}")
    print(f"   总数据点: {total_points:,}")
    print(f"   结果文件: final_download_results.json")
    
    return final_results

def merge_all_batch_files():
    """合并所有批次CSV文件"""
    print(f"\n📁 合并所有批次CSV文件")
    print("=" * 60)
    
    import pandas as pd
    
    # 找到所有批次文件
    batch_files = []
    for file in os.listdir('planet_data'):
        if file.startswith('batch_') and file.endswith('.csv'):
            batch_files.append(f'planet_data/{file}')
    
    batch_files.sort()
    print(f"找到 {len(batch_files)} 个批次文件")
    
    if not batch_files:
        print("❌ 没有找到批次文件")
        return
    
    # 合并所有数据
    all_dataframes = []
    total_points = 0
    
    for file in batch_files:
        try:
            df = pd.read_csv(file)
            all_dataframes.append(df)
            total_points += len(df)
            
            if len(all_dataframes) % 20 == 0:
                print(f"   已加载 {len(all_dataframes)} 个文件...")
                
        except Exception as e:
            print(f"   ❌ 加载失败: {file} - {e}")
    
    if all_dataframes:
        print(f"合并 {len(all_dataframes)} 个文件...")
        
        # 合并数据
        combined_df = pd.concat(all_dataframes, ignore_index=True)
        
        # 按时间排序
        combined_df['datetime'] = pd.to_datetime(combined_df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
        combined_df = combined_df.sort_values('datetime').reset_index(drop=True)
        combined_df = combined_df.drop('datetime', axis=1)
        
        # 去重
        original_length = len(combined_df)
        combined_df = combined_df.drop_duplicates(subset=['julian_date'], keep='first')
        duplicates_removed = original_length - len(combined_df)
        
        # 保存最终数据
        final_file = 'planet_data/木星_严格时间序列_完整数据.csv'
        combined_df.to_csv(final_file, index=False, encoding='utf-8-sig')
        
        print(f"\n✅ 数据合并完成:")
        print(f"   原始数据点: {total_points:,}")
        print(f"   合并后: {len(combined_df):,}")
        print(f"   移除重复: {duplicates_removed}")
        print(f"   最终文件: {final_file}")
        
        return combined_df
    else:
        print("❌ 没有成功加载的数据文件")
        return None

def main():
    """主函数"""
    print("🪐 木星数据自动下载系统")
    print("=" * 60)
    
    # 1. 下载所有剩余批次
    download_remaining_batches()
    
    # 2. 合并下载结果
    final_results = merge_all_results()
    
    # 3. 合并所有CSV文件
    final_df = merge_all_batch_files()
    
    # 4. 最终报告
    if final_df is not None:
        print(f"\n🎉 木星数据下载完成！")
        print(f"严格时间序列数据已生成")
        print(f"数据点数: {len(final_df):,}")
        print(f"文件位置: planet_data/木星_严格时间序列_完整数据.csv")
    else:
        print(f"\n❌ 数据合并失败")

if __name__ == "__main__":
    main()
