#!/usr/bin/env python3
"""
检查木星数据的时间间隔严格性
验证是否严格按照木星日间隔，以及恢复的数据点位置是否正确
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

def analyze_time_intervals():
    """分析时间间隔的严格性"""
    print("🔍 木星数据时间间隔严格性分析")
    print("=" * 60)
    
    # 读取完整版数据
    file_path = 'planet_data/木星_完整公转周期_596分钟间隔_轨道数据_完整版.csv'
    df = pd.read_csv(file_path)
    
    print(f"📊 数据基本信息:")
    print(f"  总数据点: {len(df):,}")
    print(f"  时间范围: {df['date'].iloc[0]} 到 {df['date'].iloc[-1]}")
    
    # 转换时间格式
    df['datetime'] = pd.to_datetime(df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
    df = df.sort_values('datetime').reset_index(drop=True)
    
    # 计算时间间隔
    time_diffs = df['datetime'].diff().dropna()
    time_diffs_hours = time_diffs.dt.total_seconds() / 3600
    time_diffs_minutes = time_diffs.dt.total_seconds() / 60
    
    # 目标间隔
    target_interval_hours = 9.9250  # 木星日精确值
    target_interval_minutes = 596   # JPL格式
    tolerance_minutes = 5  # 5分钟容差
    
    print(f"\n⏰ 时间间隔分析:")
    print(f"  目标木星日: {target_interval_hours:.4f} 小时")
    print(f"  JPL格式: {target_interval_minutes} 分钟")
    print(f"  平均间隔: {time_diffs_hours.mean():.4f} 小时 ({time_diffs_minutes.mean():.1f} 分钟)")
    print(f"  标准差: {time_diffs_hours.std():.4f} 小时 ({time_diffs_minutes.std():.1f} 分钟)")
    print(f"  最小间隔: {time_diffs_hours.min():.4f} 小时 ({time_diffs_minutes.min():.1f} 分钟)")
    print(f"  最大间隔: {time_diffs_hours.max():.4f} 小时 ({time_diffs_minutes.max():.1f} 分钟)")
    
    # 分析间隔分布
    print(f"\n📈 间隔分布分析:")
    
    # 标准间隔（596分钟左右）
    normal_intervals = time_diffs_minutes[
        (time_diffs_minutes >= target_interval_minutes - tolerance_minutes) & 
        (time_diffs_minutes <= target_interval_minutes + tolerance_minutes)
    ]
    
    # 异常间隔
    abnormal_intervals = time_diffs_minutes[
        (time_diffs_minutes < target_interval_minutes - tolerance_minutes) | 
        (time_diffs_minutes > target_interval_minutes + tolerance_minutes)
    ]
    
    print(f"  标准间隔 ({target_interval_minutes}±{tolerance_minutes}分钟): {len(normal_intervals)} 个 ({len(normal_intervals)/len(time_diffs_minutes)*100:.1f}%)")
    print(f"  异常间隔: {len(abnormal_intervals)} 个 ({len(abnormal_intervals)/len(time_diffs_minutes)*100:.1f}%)")
    
    if len(abnormal_intervals) > 0:
        print(f"  异常间隔范围: {abnormal_intervals.min():.1f} - {abnormal_intervals.max():.1f} 分钟")
    
    # 检查是否有严格的596分钟间隔
    exact_596 = time_diffs_minutes[np.abs(time_diffs_minutes - 596) < 0.1]
    print(f"  精确596分钟间隔: {len(exact_596)} 个 ({len(exact_596)/len(time_diffs_minutes)*100:.1f}%)")
    
    return df, time_diffs_minutes, abnormal_intervals

def find_irregular_intervals(df, time_diffs_minutes, abnormal_intervals):
    """找出不规律间隔的具体位置"""
    print(f"\n🔍 不规律间隔详细分析:")
    print("=" * 60)
    
    if len(abnormal_intervals) == 0:
        print("✅ 所有时间间隔都在正常范围内！")
        return
    
    # 找出异常间隔的位置
    tolerance_minutes = 5
    target_interval_minutes = 596
    
    abnormal_indices = []
    for i, interval in enumerate(time_diffs_minutes):
        if (interval < target_interval_minutes - tolerance_minutes) or (interval > target_interval_minutes + tolerance_minutes):
            abnormal_indices.append(i + 1)  # +1因为diff后索引偏移
    
    print(f"发现 {len(abnormal_indices)} 个异常间隔:")
    
    for i, idx in enumerate(abnormal_indices[:20]):  # 只显示前20个
        if idx < len(df):
            interval_minutes = time_diffs_minutes.iloc[idx-1]
            time_before = df.loc[idx-1, 'datetime']
            time_after = df.loc[idx, 'datetime']
            
            print(f"  {i+1:2d}. 位置 {idx:4d}: {interval_minutes:6.1f} 分钟")
            print(f"      {time_before.strftime('%Y-%m-%d %H:%M')} → {time_after.strftime('%Y-%m-%d %H:%M')}")
            
            # 检查是否是恢复的数据点附近
            date_str = time_after.strftime('%Y-%m-%d')
            if '2016-08-15' in date_str or '2017-03-22' in date_str:
                print(f"      ⭐ 这是恢复数据点附近！")
    
    if len(abnormal_indices) > 20:
        print(f"  ... 还有 {len(abnormal_indices) - 20} 个异常间隔")

def check_recovered_data_integration():
    """检查恢复数据的整合情况"""
    print(f"\n🔄 恢复数据整合检查:")
    print("=" * 60)
    
    # 读取恢复前后的数据
    original_file = 'planet_data/木星_完整公转周期_596分钟间隔_轨道数据_完整版.csv'
    recovered_file = 'planet_data/木星_最终完整数据_含恢复.csv'
    
    try:
        df_original = pd.read_csv(original_file)
        df_recovered = pd.read_csv(recovered_file)
        
        print(f"📊 数据对比:")
        print(f"  原始数据: {len(df_original):,} 个点")
        print(f"  恢复后数据: {len(df_recovered):,} 个点")
        print(f"  新增数据: {len(df_recovered) - len(df_original)} 个点")
        
        # 转换时间
        df_original['datetime'] = pd.to_datetime(df_original['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
        df_recovered['datetime'] = pd.to_datetime(df_recovered['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
        
        # 检查目标日期附近的数据
        target_dates = ['2016-08-15', '2017-03-22']
        
        for target_date in target_dates:
            print(f"\n📅 检查 {target_date} 附近数据:")
            
            # 原始数据中该日期附近
            target_dt = datetime.strptime(target_date, '%Y-%m-%d')
            window_start = target_dt - timedelta(days=1)
            window_end = target_dt + timedelta(days=1)
            
            original_window = df_original[
                (df_original['datetime'] >= window_start) & 
                (df_original['datetime'] <= window_end)
            ].sort_values('datetime')
            
            recovered_window = df_recovered[
                (df_recovered['datetime'] >= window_start) & 
                (df_recovered['datetime'] <= window_end)
            ].sort_values('datetime')
            
            print(f"  原始数据窗口: {len(original_window)} 个点")
            print(f"  恢复后窗口: {len(recovered_window)} 个点")
            print(f"  新增点数: {len(recovered_window) - len(original_window)}")
            
            if len(recovered_window) > len(original_window):
                print(f"  ✅ 在 {target_date} 成功添加了数据点")
                
                # 显示新增的数据点
                new_points = recovered_window[~recovered_window['julian_date'].isin(original_window['julian_date'])]
                if len(new_points) > 0:
                    print(f"  新增数据点时间:")
                    for _, point in new_points.iterrows():
                        print(f"    {point['datetime'].strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                print(f"  ⚠️ {target_date} 没有新增数据点")
    
    except FileNotFoundError as e:
        print(f"❌ 文件未找到: {e}")

def analyze_time_sequence_regularity():
    """分析时间序列的规律性"""
    print(f"\n📐 时间序列规律性分析:")
    print("=" * 60)
    
    file_path = 'planet_data/木星_完整公转周期_596分钟间隔_轨道数据_完整版.csv'
    df = pd.read_csv(file_path)
    df['datetime'] = pd.to_datetime(df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
    df = df.sort_values('datetime').reset_index(drop=True)
    
    # 计算理论时间序列
    start_time = df['datetime'].iloc[0]
    target_interval = timedelta(minutes=596)
    
    print(f"📊 理论vs实际时间序列对比:")
    print(f"  起始时间: {start_time}")
    print(f"  理论间隔: {target_interval}")
    
    # 检查前100个点的规律性
    print(f"\n🔍 前100个数据点的时间规律性:")
    
    perfect_matches = 0
    for i in range(min(100, len(df))):
        theoretical_time = start_time + i * target_interval
        actual_time = df.loc[i, 'datetime']
        time_diff = abs((actual_time - theoretical_time).total_seconds())
        
        if time_diff < 60:  # 1分钟容差
            perfect_matches += 1
        
        if i < 10:  # 显示前10个
            print(f"  点 {i+1:2d}: 理论 {theoretical_time.strftime('%H:%M:%S')} | 实际 {actual_time.strftime('%H:%M:%S')} | 差异 {time_diff:.0f}秒")
    
    print(f"\n📈 规律性统计:")
    print(f"  完美匹配 (±1分钟): {perfect_matches}/100 ({perfect_matches}%)")
    
    if perfect_matches >= 95:
        print(f"  ✅ 时间序列高度规律！")
    elif perfect_matches >= 80:
        print(f"  📈 时间序列较为规律")
    else:
        print(f"  ⚠️ 时间序列存在不规律性")

def main():
    """主函数"""
    print("🪐 木星数据时间间隔严格性检查")
    print("=" * 60)
    print("检查目标：")
    print("1. 验证时间间隔是否严格按照木星日")
    print("2. 检查恢复数据点的插入位置是否正确")
    print("3. 分析整体时间序列的规律性")
    
    # 分析1：时间间隔分析
    df, time_diffs_minutes, abnormal_intervals = analyze_time_intervals()
    
    # 分析2：找出不规律间隔
    find_irregular_intervals(df, time_diffs_minutes, abnormal_intervals)
    
    # 分析3：检查恢复数据整合
    check_recovered_data_integration()
    
    # 分析4：时间序列规律性
    analyze_time_sequence_regularity()
    
    # 总结
    print(f"\n📋 总结:")
    print("=" * 60)
    
    normal_percentage = (len(time_diffs_minutes) - len(abnormal_intervals)) / len(time_diffs_minutes) * 100
    
    if normal_percentage >= 98:
        print(f"✅ 时间间隔高度规律 ({normal_percentage:.1f}%标准间隔)")
        print("✅ 数据质量优秀，适合精确的轨道分析")
    elif normal_percentage >= 95:
        print(f"📈 时间间隔较为规律 ({normal_percentage:.1f}%标准间隔)")
        print("📈 数据质量良好，适合大多数分析")
    else:
        print(f"⚠️ 时间间隔存在不规律性 ({normal_percentage:.1f}%标准间隔)")
        print("⚠️ 建议进一步检查数据质量")
    
    print(f"\n关于恢复数据点的结论：")
    print("恢复的数据点应该是插入到原有时间序列的正确位置，")
    print("而不是简单追加到末尾。这样才能保持时间的连续性。")

if __name__ == "__main__":
    main()
