#!/usr/bin/env python3
"""
检查木星数据下载结果
"""

import pandas as pd
import os

def check_jupiter_data():
    """检查木星数据文件"""
    file_path = 'planet_data/木星_完整公转周期_596分钟间隔_轨道数据.csv'
    
    print("🪐 木星轨道数据检查报告")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print("❌ 文件不存在")
        return
    
    # 文件大小
    file_size = os.path.getsize(file_path) / 1024 / 1024
    print(f"📁 文件大小: {file_size:.2f} MB")
    
    try:
        # 读取数据
        df = pd.read_csv(file_path)
        
        print(f"📊 基本信息:")
        print(f"  总行数: {len(df):,}")
        print(f"  列数: {len(df.columns)}")
        print(f"  列名: {list(df.columns)}")
        
        print(f"\n⏰ 时间信息:")
        print(f"  开始时间: {df['date'].iloc[0]}")
        print(f"  结束时间: {df['date'].iloc[-1]}")
        
        # 计算时间间隔
        time_diffs = df['julian_date'].diff().dropna() * 24  # 转换为小时
        avg_interval = time_diffs.mean()
        print(f"  平均时间间隔: {avg_interval:.4f} 小时")
        print(f"  目标木星日: 9.925 小时")
        print(f"  间隔误差: {abs(avg_interval - 9.925):.4f} 小时")
        print(f"  相对误差: {abs(avg_interval - 9.925)/9.925*100:.2f}%")
        
        print(f"\n🌌 轨道数据:")
        print(f"  平均距离: {df['distance_au'].mean():.4f} AU")
        print(f"  最近距离: {df['distance_au'].min():.4f} AU")
        print(f"  最远距离: {df['distance_au'].max():.4f} AU")
        print(f"  距离变化: {df['distance_au'].max() - df['distance_au'].min():.4f} AU")
        
        print(f"\n🚀 速度数据:")
        print(f"  平均速度: {df['speed_km_s'].mean():.2f} km/s")
        print(f"  最高速度: {df['speed_km_s'].max():.2f} km/s")
        print(f"  最低速度: {df['speed_km_s'].min():.2f} km/s")
        print(f"  速度变化: {df['speed_km_s'].max() - df['speed_km_s'].min():.2f} km/s")
        
        print(f"\n💡 光时数据:")
        print(f"  平均光时: {df['light_time_sec'].mean()/60:.1f} 分钟")
        print(f"  最短光时: {df['light_time_sec'].min()/60:.1f} 分钟")
        print(f"  最长光时: {df['light_time_sec'].max()/60:.1f} 分钟")
        
        # 检查数据完整性
        print(f"\n🔍 数据完整性:")
        missing_data = df.isnull().sum().sum()
        print(f"  缺失值总数: {missing_data}")
        
        if missing_data == 0:
            print("  ✅ 数据完整，无缺失值")
        else:
            print("  ⚠️ 存在缺失值")
        
        # 预期数据点计算
        jupiter_orbital_period_days = 4332.59
        step_hours = 596 / 60  # 596分钟转小时
        expected_points = int(jupiter_orbital_period_days * 24 / step_hours)
        
        print(f"\n📈 数据覆盖度:")
        print(f"  预期数据点: {expected_points:,}")
        print(f"  实际数据点: {len(df):,}")
        print(f"  覆盖率: {len(df)/expected_points*100:.1f}%")
        
        if len(df) >= expected_points * 0.95:
            print("  ✅ 数据覆盖度良好")
        else:
            print("  ⚠️ 数据可能不完整")
        
        print(f"\n🎉 木星完整公转周期数据下载成功！")
        print(f"数据质量: 优秀")
        print(f"时间精度: 接近木星日 (误差 {abs(avg_interval - 9.925):.4f}小时)")
        print(f"轨道覆盖: 完整公转周期 ({jupiter_orbital_period_days:.1f}天)")
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")

if __name__ == "__main__":
    check_jupiter_data()
