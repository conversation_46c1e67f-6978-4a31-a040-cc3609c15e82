#!/usr/bin/env python3
"""
补充下载木星缺失数据
"""

import os
import pandas as pd
import numpy as np
import json
import warnings
import ssl
import urllib3
import time
from datetime import datetime

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

class JupiterDataFiller:
    def __init__(self):
        """初始化补充下载器"""
        self.step_size = '596m'  # 596分钟，接近木星日
        self.original_file = 'planet_data/木星_完整公转周期_596分钟间隔_轨道数据.csv'
        self.temp_file = 'planet_data/木星_补充数据_临时.csv'
        self.final_file = 'planet_data/木星_完整公转周期_596分钟间隔_轨道数据_完整版.csv'
        
    def load_tasks(self):
        """加载下载任务"""
        try:
            with open('missing_data_tasks.json', 'r', encoding='utf-8') as f:
                tasks = json.load(f)
            
            # 转换时间字符串回datetime
            for task in tasks:
                task['start_datetime'] = datetime.fromisoformat(task['start_datetime'])
                task['end_datetime'] = datetime.fromisoformat(task['end_datetime'])
            
            return tasks
        except FileNotFoundError:
            print("❌ 未找到任务文件，请先运行 找出缺失数据.py")
            return []
    
    def download_batch(self, task):
        """下载单个批次的缺失数据"""
        try:
            from astroquery.jplhorizons import Horizons
            
            print(f"📦 下载: {task['start']} 到 {task['end']}")
            
            jupiter = Horizons(
                id='599',  # 木星
                location='@sun',
                epochs={
                    'start': task['start'],
                    'stop': task['end'],
                    'step': self.step_size
                }
            )
            
            vectors = jupiter.vectors()
            df = vectors.to_pandas()
            
            # 处理数据
            processed_df = self.process_data(df)
            
            print(f"✅ 获取 {len(processed_df)} 个数据点")
            return processed_df
            
        except Exception as e:
            print(f"❌ 下载失败: {e}")
            return None
    
    def process_data(self, df):
        """处理原始数据"""
        # 单位转换常数
        au_to_km = 149597870.7
        day_to_sec = 86400
        light_speed_au_per_sec = 1.0 / 499.004783836
        
        processed_df = pd.DataFrame()
        
        # 基本列
        processed_df['julian_date'] = df['datetime_jd']
        processed_df['date'] = df['datetime_str']
        processed_df['x_position_au'] = df['x']
        processed_df['y_position_au'] = df['y']
        processed_df['z_position_au'] = df['z']
        
        # 速度转换 (AU/day -> km/s)
        processed_df['x_velocity_km_s'] = df['vx'] * au_to_km / day_to_sec
        processed_df['y_velocity_km_s'] = df['vy'] * au_to_km / day_to_sec
        processed_df['z_velocity_km_s'] = df['vz'] * au_to_km / day_to_sec
        
        # 距离
        processed_df['distance_au'] = df['range']
        processed_df['distance_km'] = df['range'] * au_to_km
        
        # 光时 (秒)
        processed_df['light_time_sec'] = df['range'] / light_speed_au_per_sec
        
        # 距离变化率 (AU/day -> km/s)
        processed_df['range_rate_km_s'] = df['range_rate'] * au_to_km / day_to_sec
        
        # 轨道速度 (km/s)
        processed_df['speed_km_s'] = np.sqrt(
            processed_df['x_velocity_km_s']**2 + 
            processed_df['y_velocity_km_s']**2 + 
            processed_df['z_velocity_km_s']**2
        )
        
        return processed_df
    
    def fill_missing_data(self):
        """执行补充下载"""
        print("🔧 木星数据补充下载器")
        print("=" * 60)
        
        # 加载任务
        tasks = self.load_tasks()
        if not tasks:
            return False
        
        print(f"📋 发现 {len(tasks)} 个补充下载任务")
        
        # 读取原始数据
        print("📖 读取原始数据...")
        original_df = pd.read_csv(self.original_file)
        print(f"原始数据: {len(original_df):,} 个数据点")
        
        # 收集所有补充数据
        all_new_data = []
        successful_tasks = 0
        
        for i, task in enumerate(tasks, 1):
            try:
                print(f"\n🚀 任务 {i}/{len(tasks)}")
                
                # 下载数据
                new_data = self.download_batch(task)
                
                if new_data is not None and len(new_data) > 0:
                    all_new_data.append(new_data)
                    successful_tasks += 1
                    
                    print(f"✅ 成功，累计新增: {sum(len(df) for df in all_new_data)} 个点")
                else:
                    print("⚠️ 本批次无数据")
                
                # 批次间延迟
                if i < len(tasks):
                    print("⏳ 等待2秒...")
                    time.sleep(2)
                    
            except Exception as e:
                print(f"❌ 任务 {i} 失败: {e}")
                continue
        
        # 合并所有数据
        if all_new_data:
            print(f"\n🔄 合并数据...")
            
            # 合并所有新数据
            new_df = pd.concat(all_new_data, ignore_index=True)
            print(f"新增数据: {len(new_df):,} 个数据点")
            
            # 与原始数据合并
            combined_df = pd.concat([original_df, new_df], ignore_index=True)
            
            # 按时间排序
            combined_df['datetime'] = pd.to_datetime(combined_df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
            combined_df = combined_df.sort_values('datetime').reset_index(drop=True)
            
            # 删除临时datetime列
            combined_df = combined_df.drop('datetime', axis=1)
            
            # 去除可能的重复数据
            original_length = len(combined_df)
            combined_df = combined_df.drop_duplicates(subset=['julian_date'], keep='first')
            duplicates_removed = original_length - len(combined_df)
            
            if duplicates_removed > 0:
                print(f"🔄 移除重复数据: {duplicates_removed} 个")
            
            # 保存完整数据
            combined_df.to_csv(self.final_file, index=False, encoding='utf-8-sig')
            
            print(f"\n🎉 数据补充完成！")
            print(f"原始数据: {len(original_df):,} 个点")
            print(f"新增数据: {len(new_df):,} 个点")
            print(f"最终数据: {len(combined_df):,} 个点")
            print(f"成功任务: {successful_tasks}/{len(tasks)}")
            print(f"完整文件: {self.final_file}")
            
            # 计算新的覆盖率
            self.calculate_final_coverage(combined_df)
            
            return True
        else:
            print("❌ 未获取到任何新数据")
            return False
    
    def calculate_final_coverage(self, df):
        """计算最终覆盖率"""
        print(f"\n📊 最终覆盖率计算:")
        
        # 转换时间
        df['datetime'] = pd.to_datetime(df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
        
        # 计算总时长
        start_time = df['datetime'].iloc[0]
        end_time = df['datetime'].iloc[-1]
        total_duration = (end_time - start_time).total_seconds() / (24 * 3600)  # 天
        
        # 理论数据点
        target_interval_hours = 596 / 60  # 9.9333小时
        theoretical_points = total_duration * 24 / target_interval_hours
        
        # 覆盖率
        coverage_rate = len(df) / theoretical_points * 100
        
        print(f"  时间跨度: {total_duration:.1f} 天")
        print(f"  理论数据点: {theoretical_points:.0f}")
        print(f"  实际数据点: {len(df):,}")
        print(f"  最终覆盖率: {coverage_rate:.2f}%")
        
        if coverage_rate >= 99:
            print(f"  🌟 覆盖率优秀！接近完美！")
        elif coverage_rate >= 98:
            print(f"  ✅ 覆盖率非常好！")
        else:
            print(f"  📈 覆盖率已显著提升！")

def main():
    """主函数"""
    filler = JupiterDataFiller()
    
    print("🪐 木星数据补充下载")
    print("=" * 60)
    print("将补充下载缺失的数据，提升覆盖率到接近100%")
    
    success = filler.fill_missing_data()
    
    if success:
        print(f"\n✅ 补充下载完成！")
        print("现在您拥有了接近完整的木星公转周期数据。")
    else:
        print(f"\n❌ 补充下载失败")

if __name__ == "__main__":
    main()
