#!/usr/bin/env python3
"""
合并恢复的缺失数据到完整数据集
"""

import pandas as pd
import os

def merge_recovered_data():
    """合并恢复的数据"""
    print("🔄 合并恢复的缺失数据")
    print("=" * 60)
    
    # 找到现有的完整数据文件
    possible_files = [
        'planet_data/木星_完整数据_含2025年1月.csv',
        'planet_data/木星_完整公转周期_596分钟间隔_轨道数据_完整版.csv',
        'planet_data/木星_完整公转周期_596分钟间隔_轨道数据.csv'
    ]
    
    existing_file = None
    for file in possible_files:
        if os.path.exists(file):
            existing_file = file
            break
    
    if existing_file is None:
        print("❌ 未找到现有的木星数据文件")
        return False
    
    print(f"📖 读取现有数据: {existing_file}")
    existing_df = pd.read_csv(existing_file)
    print(f"现有数据点: {len(existing_df):,}")
    
    # 读取恢复的数据
    recovered_files = [
        'planet_data/木星_恢复数据_2016-08-15.csv',
        'planet_data/木星_恢复数据_2017-03-22.csv'
    ]
    
    all_recovered_data = []
    total_recovered_points = 0
    
    for file in recovered_files:
        if os.path.exists(file):
            df = pd.read_csv(file)
            all_recovered_data.append(df)
            total_recovered_points += len(df)
            print(f"📖 读取恢复数据: {file} ({len(df)} 个点)")
        else:
            print(f"⚠️ 恢复文件不存在: {file}")
    
    if not all_recovered_data:
        print("❌ 没有找到恢复的数据文件")
        return False
    
    # 合并恢复的数据
    recovered_df = pd.concat(all_recovered_data, ignore_index=True)
    print(f"📊 总恢复数据点: {total_recovered_points}")
    
    # 与现有数据合并
    print(f"🔄 合并数据...")
    combined_df = pd.concat([existing_df, recovered_df], ignore_index=True)
    
    # 按时间排序
    combined_df['datetime'] = pd.to_datetime(combined_df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
    combined_df = combined_df.sort_values('datetime').reset_index(drop=True)
    combined_df = combined_df.drop('datetime', axis=1)
    
    # 去重（基于儒略日）
    original_length = len(combined_df)
    combined_df = combined_df.drop_duplicates(subset=['julian_date'], keep='first')
    duplicates_removed = original_length - len(combined_df)
    
    print(f"📈 合并结果:")
    print(f"  原始数据: {len(existing_df):,} 个点")
    print(f"  恢复数据: {total_recovered_points} 个点")
    print(f"  合并后: {len(combined_df):,} 个点")
    print(f"  净增加: {len(combined_df) - len(existing_df)} 个点")
    
    if duplicates_removed > 0:
        print(f"  移除重复: {duplicates_removed} 个点")
    
    # 保存最终数据
    final_file = 'planet_data/木星_最终完整数据_含恢复.csv'
    combined_df.to_csv(final_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 最终数据已保存: {final_file}")
    
    # 重新计算覆盖率
    calculate_final_coverage(combined_df)
    
    return True

def calculate_final_coverage(df):
    """计算最终覆盖率"""
    print(f"\n📊 最终覆盖率计算")
    print("=" * 60)
    
    # 转换时间
    df['datetime'] = pd.to_datetime(df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
    
    start_time = df['datetime'].iloc[0]
    end_time = df['datetime'].iloc[-1]
    total_duration = (end_time - start_time).total_seconds() / (24 * 3600)  # 天
    
    # 理论数据点
    target_interval_hours = 596 / 60  # 9.9333小时
    theoretical_points = total_duration * 24 / target_interval_hours
    
    # 覆盖率
    coverage_rate = len(df) / theoretical_points * 100
    
    print(f"📅 时间信息:")
    print(f"  开始时间: {start_time}")
    print(f"  结束时间: {end_time}")
    print(f"  总时长: {total_duration:.1f} 天 ({total_duration/365.25:.2f} 年)")
    
    print(f"\n📈 覆盖率分析:")
    print(f"  理论数据点: {theoretical_points:.0f}")
    print(f"  实际数据点: {len(df):,}")
    print(f"  最终覆盖率: {coverage_rate:.4f}%")
    
    # 检查时间连续性
    time_diffs = df['datetime'].diff().dropna()
    time_diffs_hours = time_diffs.dt.total_seconds() / 3600
    
    # 找出大间隙
    large_gaps = time_diffs_hours[time_diffs_hours > target_interval_hours + 2]
    
    print(f"\n🔍 数据连续性:")
    print(f"  平均间隔: {time_diffs_hours.mean():.4f} 小时")
    print(f"  目标间隔: {target_interval_hours:.4f} 小时")
    print(f"  大间隙数量: {len(large_gaps)}")
    
    if len(large_gaps) == 0:
        print(f"  ✅ 数据连续性: 完美！无明显间隙")
    elif len(large_gaps) <= 2:
        print(f"  ✅ 数据连续性: 优秀！仅有{len(large_gaps)}个小间隙")
    else:
        print(f"  ⚠️ 数据连续性: 良好，有{len(large_gaps)}个间隙")
    
    # 质量评估
    if coverage_rate >= 100.7:
        quality = "完美"
        emoji = "🌟"
    elif coverage_rate >= 100.0:
        quality = "优秀"
        emoji = "✅"
    elif coverage_rate >= 99.5:
        quality = "良好"
        emoji = "📈"
    else:
        quality = "可接受"
        emoji = "⚠️"
    
    print(f"\n{emoji} 数据质量评估: {quality}")
    print(f"覆盖率: {coverage_rate:.4f}%")
    
    if coverage_rate >= 100.7:
        print("🎉 恭喜！您现在拥有了超完整的木星轨道数据集！")
    elif coverage_rate >= 100.0:
        print("🎉 恭喜！您现在拥有了完整的木星轨道数据集！")
    else:
        print("📈 数据质量已显著提升！")

def verify_recovered_dates():
    """验证恢复的日期数据"""
    print(f"\n🔍 验证恢复的日期数据")
    print("=" * 60)
    
    final_file = 'planet_data/木星_最终完整数据_含恢复.csv'
    if not os.path.exists(final_file):
        print("❌ 最终数据文件不存在")
        return
    
    df = pd.read_csv(final_file)
    
    # 检查目标日期
    target_dates = ['2016-Aug-15', '2017-Mar-22']
    
    for target_date in target_dates:
        date_data = df[df['date'].str.contains(target_date)]
        
        if len(date_data) > 0:
            print(f"✅ {target_date}: 找到 {len(date_data)} 个数据点")
            print(f"   时间范围: {date_data['date'].iloc[0]} 到 {date_data['date'].iloc[-1]}")
            print(f"   距离范围: {date_data['distance_au'].min():.4f} - {date_data['distance_au'].max():.4f} AU")
        else:
            print(f"❌ {target_date}: 未找到数据")

def main():
    """主函数"""
    print("🪐 木星缺失数据合并工具")
    print("=" * 60)
    print("将恢复的2016年8月15日、2017年3月22日数据合并到完整数据集")
    
    success = merge_recovered_data()
    
    if success:
        verify_recovered_dates()
        
        print(f"\n🎉 数据合并完成！")
        print("现在您拥有了包含恢复数据的完整木星轨道数据集。")
        print("文件: planet_data/木星_最终完整数据_含恢复.csv")
    else:
        print(f"\n❌ 数据合并失败")

if __name__ == "__main__":
    main()
