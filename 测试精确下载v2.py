#!/usr/bin/env python3
"""
测试精确时间序列下载v2.0 - 只下载前3个批次验证
"""

import os
import json
import pandas as pd
import warnings
import ssl
import urllib3

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

def test_precise_download_v2():
    """测试精确下载v2.0"""
    print("🧪 测试精确时间序列下载 v2.0")
    print("=" * 60)
    
    # 加载精确批次
    try:
        with open('jupiter_precise_download_batches.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        batches = data['batches']
        print(f"✅ 加载了 {len(batches)} 个精确批次")
    except FileNotFoundError:
        print("❌ 精确批次文件不存在")
        return
    
    # 测试前3个批次
    test_batches = batches[:3]
    print(f"🔬 测试前 {len(test_batches)} 个批次")
    
    results = []
    
    for i, batch in enumerate(test_batches, 1):
        batch_id = batch['batch_id']
        start_date = batch['jpl_start_date']
        end_date = batch['jpl_end_date']
        expected_points = batch['time_points']
        intervals_595m = batch['intervals_595m']
        intervals_596m = batch['intervals_596m']
        
        print(f"\n批次 {i}/{len(test_batches)}")
        print(f"   批次ID: {batch_id}")
        print(f"   时间范围: {start_date} 到 {end_date}")
        print(f"   期望点数: {expected_points}")
        print(f"   间隔分布: 595m×{intervals_595m}, 596m×{intervals_596m}")
        
        # 选择主要的JPL步长
        if intervals_596m >= intervals_595m:
            jpl_step = '596m'
        else:
            jpl_step = '595m'
        
        print(f"   使用步长: {jpl_step}")
        
        try:
            from astroquery.jplhorizons import Horizons
            
            jupiter = Horizons(
                id='599',
                location='@sun',
                epochs={
                    'start': start_date,
                    'stop': end_date,
                    'step': jpl_step
                }
            )
            
            vectors = jupiter.vectors()
            df = vectors.to_pandas()
            
            if len(df) > 0:
                print(f"   ✅ 成功获取 {len(df)} 个数据点")
                
                # 分析时间间隔
                df['datetime'] = pd.to_datetime(df['datetime_str'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
                df = df.sort_values('datetime')
                
                if len(df) > 1:
                    time_diffs = df['datetime'].diff().dropna()
                    avg_interval_minutes = time_diffs.dt.total_seconds().mean() / 60
                    
                    print(f"   平均间隔: {avg_interval_minutes:.3f} 分钟")
                    print(f"   与木星日差异: {abs(avg_interval_minutes - 595.5):.3f} 分钟")
                    
                    # 检查间隔分布
                    interval_minutes = time_diffs.dt.total_seconds() / 60
                    count_595 = sum(1 for x in interval_minutes if abs(x - 595) < 30)
                    count_596 = sum(1 for x in interval_minutes if abs(x - 596) < 30)
                    
                    print(f"   实际间隔分布: ~595m×{count_595}, ~596m×{count_596}")
                
                results.append({
                    'batch_id': batch_id,
                    'status': 'success',
                    'points': len(df),
                    'expected': expected_points,
                    'jpl_step': jpl_step,
                    'avg_interval_minutes': avg_interval_minutes if len(df) > 1 else None
                })
                
            else:
                print(f"   ⚠️ 无数据返回")
                results.append({
                    'batch_id': batch_id,
                    'status': 'no_data',
                    'points': 0,
                    'expected': expected_points,
                    'jpl_step': jpl_step
                })
                
        except Exception as e:
            print(f"   ❌ 下载失败: {e}")
            results.append({
                'batch_id': batch_id,
                'status': 'failed',
                'points': 0,
                'expected': expected_points,
                'jpl_step': jpl_step,
                'error': str(e)
            })
        
        print(f"   ⏳ 等待2秒...")
        import time
        time.sleep(2)
    
    # 分析测试结果
    print(f"\n📊 测试结果分析:")
    print("=" * 60)
    
    successful = [r for r in results if r['status'] == 'success']
    failed = [r for r in results if r['status'] != 'success']
    
    print(f"成功批次: {len(successful)}/{len(test_batches)}")
    print(f"失败批次: {len(failed)}")
    
    if successful:
        total_points = sum(r['points'] for r in successful)
        total_expected = sum(r['expected'] for r in successful)
        
        print(f"总下载点数: {total_points}")
        print(f"总期望点数: {total_expected}")
        print(f"点数完成率: {total_points/total_expected*100:.2f}%")
        
        # 分析时间间隔精度
        valid_intervals = [r['avg_interval_minutes'] for r in successful if r['avg_interval_minutes'] is not None]
        if valid_intervals:
            avg_of_averages = sum(valid_intervals) / len(valid_intervals)
            print(f"平均时间间隔: {avg_of_averages:.3f} 分钟")
            print(f"与精确木星日差异: {abs(avg_of_averages - 595.5):.3f} 分钟")
            
            if abs(avg_of_averages - 595.5) < 1.0:
                print(f"✅ 时间间隔精度优秀！")
            elif abs(avg_of_averages - 595.5) < 5.0:
                print(f"📈 时间间隔精度良好")
            else:
                print(f"⚠️ 时间间隔精度需要改进")
        
        print(f"\n✅ 测试成功！精确时间序列v2.0方案可行")
        print("可以继续进行完整下载")
        
        # 验证混合策略效果
        print(f"\n🔬 混合策略验证:")
        step_595_count = sum(1 for r in successful if r['jpl_step'] == '595m')
        step_596_count = sum(1 for r in successful if r['jpl_step'] == '596m')
        
        print(f"使用595m步长: {step_595_count} 个批次")
        print(f"使用596m步长: {step_596_count} 个批次")
        print(f"混合比例: {step_595_count}:{step_596_count}")
        
        if step_595_count > 0 and step_596_count > 0:
            print(f"✅ 混合策略正常工作！")
        else:
            print(f"⚠️ 混合策略可能需要调整")
    
    else:
        print(f"\n❌ 测试失败！需要检查配置")
    
    # 保存测试结果
    with open('test_precise_v2_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"测试结果已保存: test_precise_v2_results.json")

def compare_with_v1():
    """与v1版本对比"""
    print(f"\n📊 与v1版本对比")
    print("=" * 60)
    
    try:
        # 加载v1时间序列
        with open('jupiter_time_sequence.json', 'r', encoding='utf-8') as f:
            v1_data = json.load(f)
        
        # 加载v2时间序列
        with open('jupiter_precise_time_sequence.json', 'r', encoding='utf-8') as f:
            v2_data = json.load(f)
        
        print(f"版本对比:")
        print(f"  v1.0 数据点: {len(v1_data['time_sequence']):,}")
        print(f"  v2.0 数据点: {len(v2_data['time_sequence']):,}")
        print(f"  差异: {len(v2_data['time_sequence']) - len(v1_data['time_sequence'])}")
        
        print(f"\n策略对比:")
        print(f"  v1.0: 单一596分钟间隔")
        print(f"  v2.0: 595/596分钟混合策略")
        
        print(f"\n误差对比:")
        print(f"  v1.0: 累积误差 +0.5分钟/间隔")
        print(f"  v2.0: 理论零累积误差")
        
        v2_mixed = v2_data['metadata']['mixed_strategy']
        print(f"\nv2.0混合策略详情:")
        print(f"  595分钟间隔: {v2_mixed['intervals_595m']:,}")
        print(f"  596分钟间隔: {v2_mixed['intervals_596m']:,}")
        print(f"  理论总误差: {v2_mixed['total_error_minutes']:.6f} 分钟")
        
    except FileNotFoundError as e:
        print(f"无法加载文件进行对比: {e}")

def main():
    """主函数"""
    print("🪐 精确时间序列下载器 v2.0 测试")
    print("=" * 60)
    
    # 1. 测试精确下载
    test_precise_download_v2()
    
    # 2. 与v1对比
    compare_with_v1()

if __name__ == "__main__":
    main()
