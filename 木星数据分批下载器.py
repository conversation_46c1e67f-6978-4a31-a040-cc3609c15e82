#!/usr/bin/env python3
"""
木星轨道数据分批下载器
从2024.12.31往前推一个木星公转周期，按木星日间隔下载
支持分批下载，实时写入CSV，避免内存溢出
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
import ssl
import urllib3
import time

# 忽略警告
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

class JupiterBatchDownloader:
    def __init__(self):
        """初始化分批下载器"""
        self.configure_proxy()
        
        # 木星精确参数（NASA官方数据）
        self.jupiter_params = {
            'id': '599',
            'rotation_period_hours': 9.9250,
            'rotation_period_days': 0.4135416667,
            'orbital_period_days': 4332.59,  # 11.862年
            'mean_distance_au': 5.204
        }
        
        # 计算时间范围
        self.end_date = datetime(2024, 12, 31)
        self.start_date = self.end_date - timedelta(days=self.jupiter_params['orbital_period_days'])
        
        print(f"📅 计算的时间范围:")
        print(f"结束日期: {self.end_date.strftime('%Y-%m-%d')}")
        print(f"开始日期: {self.start_date.strftime('%Y-%m-%d')}")
        print(f"总天数: {self.jupiter_params['orbital_period_days']:.1f} 天")
        
        # 分批参数
        self.batch_days = 180  # 每批6个月数据
        self.step_size = f"{self.jupiter_params['rotation_period_days']:.10f}d"
        
        # 输出文件
        self.output_file = 'planet_data/木星_完整公转周期_木星日间隔_轨道数据.csv'
        
    def configure_proxy(self):
        """配置代理"""
        try:
            os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
            os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'
            print("✅ 代理配置完成: 127.0.0.1:7890")
        except Exception as e:
            print(f"⚠️ 代理配置警告: {e}")
    
    def calculate_batches(self):
        """计算分批下载的时间段"""
        batches = []
        current_start = self.start_date
        
        while current_start < self.end_date:
            current_end = min(current_start + timedelta(days=self.batch_days), self.end_date)
            batches.append({
                'start': current_start.strftime('%Y-%m-%d'),
                'end': current_end.strftime('%Y-%m-%d'),
                'days': (current_end - current_start).days
            })
            current_start = current_end
        
        return batches
    
    def download_batch(self, batch_info, batch_num, total_batches):
        """下载单个批次的数据"""
        print(f"\n🚀 下载批次 {batch_num}/{total_batches}")
        print(f"时间范围: {batch_info['start']} 到 {batch_info['end']}")
        print(f"天数: {batch_info['days']} 天")
        print(f"步长: {self.step_size}")
        
        try:
            from astroquery.jplhorizons import Horizons
            
            # 创建木星查询对象
            jupiter = Horizons(
                id=self.jupiter_params['id'],
                location='@sun',
                epochs={
                    'start': batch_info['start'],
                    'stop': batch_info['end'],
                    'step': self.step_size
                }
            )
            
            print("正在查询JPL HORIZONS...")
            vectors = jupiter.vectors()
            
            print(f"✅ 获取到 {len(vectors)} 个数据点")
            
            # 转换为DataFrame
            df = vectors.to_pandas()
            
            # 处理数据
            processed_df = self.process_data(df)
            
            return processed_df
            
        except Exception as e:
            print(f"❌ 批次下载失败: {e}")
            return None
    
    def process_data(self, df):
        """处理原始数据"""
        # 单位转换常数
        au_to_km = 149597870.7
        day_to_sec = 86400
        light_speed_au_per_sec = 1.0 / 499.004783836
        
        # 创建处理后的DataFrame
        processed_df = pd.DataFrame()
        
        # 基本列
        processed_df['julian_date'] = df['datetime_jd']
        processed_df['date'] = df['datetime_str']
        processed_df['x_position_au'] = df['x']
        processed_df['y_position_au'] = df['y']
        processed_df['z_position_au'] = df['z']
        
        # 速度转换 (AU/day -> km/s)
        processed_df['x_velocity_km_s'] = df['vx'] * au_to_km / day_to_sec
        processed_df['y_velocity_km_s'] = df['vy'] * au_to_km / day_to_sec
        processed_df['z_velocity_km_s'] = df['vz'] * au_to_km / day_to_sec
        
        # 距离
        processed_df['distance_au'] = df['range']
        processed_df['distance_km'] = df['range'] * au_to_km
        
        # 光时 (秒)
        processed_df['light_time_sec'] = df['range'] / light_speed_au_per_sec
        
        # 距离变化率 (AU/day -> km/s)
        processed_df['range_rate_km_s'] = df['range_rate'] * au_to_km / day_to_sec
        
        # 轨道速度 (km/s)
        processed_df['speed_km_s'] = np.sqrt(
            processed_df['x_velocity_km_s']**2 + 
            processed_df['y_velocity_km_s']**2 + 
            processed_df['z_velocity_km_s']**2
        )
        
        return processed_df
    
    def append_to_csv(self, df, is_first_batch=False):
        """追加数据到CSV文件"""
        # 确保目录存在
        os.makedirs('planet_data', exist_ok=True)
        
        if is_first_batch:
            # 第一批：创建新文件并写入表头
            df.to_csv(self.output_file, index=False, encoding='utf-8-sig')
            print(f"📁 创建文件: {self.output_file}")
        else:
            # 后续批次：追加数据（不写表头）
            df.to_csv(self.output_file, mode='a', header=False, index=False, encoding='utf-8-sig')
            print(f"📁 追加数据到: {self.output_file}")
        
        print(f"   本批次数据点: {len(df)}")
    
    def show_progress_stats(self, batch_num, total_batches, df):
        """显示进度统计"""
        progress = (batch_num / total_batches) * 100
        
        print(f"\n📊 进度统计:")
        print(f"完成进度: {progress:.1f}% ({batch_num}/{total_batches})")
        print(f"本批次数据点: {len(df)}")
        
        if len(df) > 0:
            print(f"距离范围: {df['distance_au'].min():.4f} - {df['distance_au'].max():.4f} AU")
            print(f"速度范围: {df['speed_km_s'].min():.2f} - {df['speed_km_s'].max():.2f} km/s")
    
    def download_complete_orbit(self):
        """下载完整公转周期数据"""
        print("🪐 木星完整公转周期数据下载器")
        print("=" * 60)
        print(f"时间范围: {self.start_date.strftime('%Y-%m-%d')} 到 {self.end_date.strftime('%Y-%m-%d')}")
        print(f"总周期: {self.jupiter_params['orbital_period_days']:.1f} 天")
        print(f"木星日步长: {self.step_size}")
        print(f"预计数据点: ~{int(self.jupiter_params['orbital_period_days'] / self.jupiter_params['rotation_period_days'])}")
        
        # 计算分批
        batches = self.calculate_batches()
        total_batches = len(batches)
        
        print(f"\n📦 分批下载策略:")
        print(f"总批次: {total_batches}")
        print(f"每批天数: {self.batch_days} 天")
        
        # 确认开始下载
        print(f"\n⚠️ 注意：完整下载预计需要 {total_batches * 2} 分钟")
        print("是否开始下载？(y/n): ", end="")
        
        # 暂时自动确认，实际使用时可以取消注释下面的输入
        # choice = input().lower().strip()
        # if choice != 'y' and choice != 'yes':
        #     print("下载已取消")
        #     return
        
        print("y  # 自动确认")
        
        total_points = 0
        successful_batches = 0
        
        # 开始分批下载
        for i, batch in enumerate(batches, 1):
            try:
                # 下载批次数据
                df = self.download_batch(batch, i, total_batches)
                
                if df is not None and len(df) > 0:
                    # 写入CSV
                    self.append_to_csv(df, is_first_batch=(i == 1))
                    
                    # 更新统计
                    total_points += len(df)
                    successful_batches += 1
                    
                    # 显示进度
                    self.show_progress_stats(i, total_batches, df)
                    
                    # 批次间延迟，避免服务器压力
                    if i < total_batches:
                        print("⏳ 等待5秒后继续下一批次...")
                        time.sleep(5)
                else:
                    print(f"⚠️ 批次 {i} 数据为空，跳过")
                    
            except Exception as e:
                print(f"❌ 批次 {i} 下载失败: {e}")
                print("继续下载下一批次...")
                continue
        
        # 下载完成统计
        self.show_final_stats(successful_batches, total_batches, total_points)
    
    def show_final_stats(self, successful_batches, total_batches, total_points):
        """显示最终统计"""
        print("\n" + "=" * 60)
        print("🎉 下载完成！")
        print("=" * 60)
        
        print(f"成功批次: {successful_batches}/{total_batches}")
        print(f"总数据点: {total_points}")
        print(f"文件位置: {self.output_file}")
        
        if os.path.exists(self.output_file):
            file_size = os.path.getsize(self.output_file) / 1024 / 1024
            print(f"文件大小: {file_size:.2f} MB")
            
            # 验证数据
            try:
                df_final = pd.read_csv(self.output_file)
                print(f"文件验证: ✅ 共 {len(df_final)} 行数据")
                
                if len(df_final) > 0:
                    print(f"\n📊 最终数据统计:")
                    print(f"时间跨度: {df_final['date'].iloc[0]} 到 {df_final['date'].iloc[-1]}")
                    print(f"平均距离: {df_final['distance_au'].mean():.4f} AU")
                    print(f"距离范围: {df_final['distance_au'].min():.4f} - {df_final['distance_au'].max():.4f} AU")
                    print(f"平均速度: {df_final['speed_km_s'].mean():.2f} km/s")
                    print(f"速度范围: {df_final['speed_km_s'].min():.2f} - {df_final['speed_km_s'].max():.2f} km/s")
                    print(f"平均光时: {df_final['light_time_sec'].mean()/60:.1f} 分钟")
                    
            except Exception as e:
                print(f"文件验证失败: {e}")

def main():
    """主函数"""
    downloader = JupiterBatchDownloader()
    downloader.download_complete_orbit()

if __name__ == "__main__":
    main()
