#!/usr/bin/env python3
"""
木星轨道数据下载器 - 修复版
修复JPL HORIZONS步长格式问题，使用合适的时间间隔
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
import ssl
import urllib3
import time

# 忽略警告
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

class JupiterDownloaderFixed:
    def __init__(self):
        """初始化修复版下载器"""
        self.configure_proxy()
        
        # 木星精确参数
        self.jupiter_params = {
            'id': '599',
            'rotation_period_hours': 9.9250,
            'rotation_period_days': 0.4135416667,
            'orbital_period_days': 4332.59
        }
        
        # 计算时间范围：从2024.12.31往前推一个公转周期
        self.end_date = datetime(2024, 12, 31)
        self.start_date = self.end_date - timedelta(days=self.jupiter_params['orbital_period_days'])
        
        print(f"📅 时间范围:")
        print(f"开始: {self.start_date.strftime('%Y-%m-%d')}")
        print(f"结束: {self.end_date.strftime('%Y-%m-%d')}")
        print(f"总天数: {self.jupiter_params['orbital_period_days']:.1f}")
        
        # 使用更合适的步长格式
        # JPL HORIZONS更喜欢小时格式而不是高精度天数
        self.step_size = f"{self.jupiter_params['rotation_period_hours']:.4f}h"  # 9.9250h
        
        print(f"木星日步长: {self.step_size}")
        
    def configure_proxy(self):
        """配置代理"""
        try:
            os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
            os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'
            print("✅ 代理配置完成")
        except Exception as e:
            print(f"⚠️ 代理配置警告: {e}")
    
    def test_small_batch(self):
        """先测试小批量数据"""
        print("\n🧪 测试小批量下载...")
        print("=" * 50)
        
        try:
            from astroquery.jplhorizons import Horizons
            
            # 测试最近7天的数据
            test_start = '2024-12-25'
            test_end = '2024-12-31'
            
            print(f"测试时间范围: {test_start} 到 {test_end}")
            print(f"测试步长: {self.step_size}")
            
            jupiter = Horizons(
                id=self.jupiter_params['id'],
                location='@sun',
                epochs={
                    'start': test_start,
                    'stop': test_end,
                    'step': self.step_size
                }
            )
            
            print("正在查询JPL HORIZONS...")
            vectors = jupiter.vectors()
            
            print(f"✅ 测试成功！获取到 {len(vectors)} 个数据点")
            
            # 转换并保存测试数据
            df = vectors.to_pandas()
            processed_df = self.process_data(df)
            
            test_file = 'planet_data/木星_测试数据_7天.csv'
            os.makedirs('planet_data', exist_ok=True)
            processed_df.to_csv(test_file, index=False, encoding='utf-8-sig')
            
            print(f"测试数据已保存: {test_file}")
            print(f"数据点数: {len(processed_df)}")
            
            # 显示数据预览
            print(f"\n📋 数据预览:")
            print(processed_df.head(3))
            
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    def download_monthly_batches(self):
        """按月分批下载完整数据"""
        print("\n🚀 开始分月下载完整公转周期数据...")
        print("=" * 60)
        
        # 计算月份批次
        current_date = self.start_date
        batches = []
        
        while current_date < self.end_date:
            # 每批一个月
            if current_date.month == 12:
                next_month = current_date.replace(year=current_date.year + 1, month=1, day=1)
            else:
                next_month = current_date.replace(month=current_date.month + 1, day=1)
            
            batch_end = min(next_month, self.end_date)
            
            batches.append({
                'start': current_date.strftime('%Y-%m-%d'),
                'end': batch_end.strftime('%Y-%m-%d'),
                'days': (batch_end - current_date).days
            })
            
            current_date = batch_end
        
        print(f"总批次数: {len(batches)}")
        print(f"预计总时间: {len(batches) * 2} 分钟")
        
        # 输出文件
        output_file = 'planet_data/木星_完整公转周期_木星日间隔_轨道数据.csv'
        
        total_points = 0
        successful_batches = 0
        
        for i, batch in enumerate(batches, 1):
            try:
                print(f"\n📦 批次 {i}/{len(batches)}")
                print(f"时间: {batch['start']} 到 {batch['end']} ({batch['days']}天)")
                
                # 下载批次数据
                df = self.download_batch(batch)
                
                if df is not None and len(df) > 0:
                    # 写入CSV
                    if i == 1:
                        df.to_csv(output_file, index=False, encoding='utf-8-sig')
                        print(f"📁 创建文件: {output_file}")
                    else:
                        df.to_csv(output_file, mode='a', header=False, index=False, encoding='utf-8-sig')
                        print(f"📁 追加数据到: {output_file}")
                    
                    total_points += len(df)
                    successful_batches += 1
                    
                    print(f"✅ 本批次: {len(df)} 个数据点")
                    print(f"📊 累计: {total_points} 个数据点")
                    
                    # 批次间延迟
                    if i < len(batches):
                        print("⏳ 等待3秒...")
                        time.sleep(3)
                else:
                    print("⚠️ 本批次数据为空")
                    
            except Exception as e:
                print(f"❌ 批次 {i} 失败: {e}")
                continue
        
        # 最终统计
        print(f"\n🎉 下载完成！")
        print(f"成功批次: {successful_batches}/{len(batches)}")
        print(f"总数据点: {total_points}")
        print(f"文件: {output_file}")
        
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / 1024 / 1024
            print(f"文件大小: {file_size:.2f} MB")
    
    def download_batch(self, batch_info):
        """下载单个批次"""
        try:
            from astroquery.jplhorizons import Horizons
            
            jupiter = Horizons(
                id=self.jupiter_params['id'],
                location='@sun',
                epochs={
                    'start': batch_info['start'],
                    'stop': batch_info['end'],
                    'step': self.step_size
                }
            )
            
            vectors = jupiter.vectors()
            df = vectors.to_pandas()
            processed_df = self.process_data(df)
            
            return processed_df
            
        except Exception as e:
            print(f"批次下载错误: {e}")
            return None
    
    def process_data(self, df):
        """处理原始数据"""
        # 单位转换常数
        au_to_km = 149597870.7
        day_to_sec = 86400
        light_speed_au_per_sec = 1.0 / 499.004783836
        
        processed_df = pd.DataFrame()
        
        # 基本列
        processed_df['julian_date'] = df['datetime_jd']
        processed_df['date'] = df['datetime_str']
        processed_df['x_position_au'] = df['x']
        processed_df['y_position_au'] = df['y']
        processed_df['z_position_au'] = df['z']
        
        # 速度转换
        processed_df['x_velocity_km_s'] = df['vx'] * au_to_km / day_to_sec
        processed_df['y_velocity_km_s'] = df['vy'] * au_to_km / day_to_sec
        processed_df['z_velocity_km_s'] = df['vz'] * au_to_km / day_to_sec
        
        # 距离
        processed_df['distance_au'] = df['range']
        processed_df['distance_km'] = df['range'] * au_to_km
        
        # 光时
        processed_df['light_time_sec'] = df['range'] / light_speed_au_per_sec
        
        # 距离变化率
        processed_df['range_rate_km_s'] = df['range_rate'] * au_to_km / day_to_sec
        
        # 轨道速度
        processed_df['speed_km_s'] = np.sqrt(
            processed_df['x_velocity_km_s']**2 + 
            processed_df['y_velocity_km_s']**2 + 
            processed_df['z_velocity_km_s']**2
        )
        
        return processed_df

def main():
    """主函数"""
    print("🪐 木星轨道数据下载器 - 修复版")
    print("=" * 60)
    
    downloader = JupiterDownloaderFixed()
    
    # 先测试小批量
    test_success = downloader.test_small_batch()
    
    if test_success:
        print("\n✅ 小批量测试成功！")
        print("是否继续下载完整数据？")
        print("注意：完整下载需要约2-3小时")
        
        # 自动继续（实际使用时可以取消注释输入确认）
        # choice = input("继续？(y/n): ").lower().strip()
        # if choice == 'y' or choice == 'yes':
        print("y  # 自动继续")
        downloader.download_monthly_batches()
        # else:
        #     print("下载已取消")
    else:
        print("❌ 测试失败，请检查网络连接和代理设置")

if __name__ == "__main__":
    main()
