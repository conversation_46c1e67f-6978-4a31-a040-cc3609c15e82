#!/usr/bin/env python3
"""
监控实用精确下载进度
"""

import os
import json
import time
from datetime import datetime

def monitor_practical_download():
    """监控实用下载进度"""
    print("📊 实用精确下载进度监控")
    print("=" * 60)
    
    # 获取总批次数
    try:
        with open('jupiter_practical_download_plan.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        total_batches = len(data['batches'])
    except:
        print("❌ 无法读取下载计划")
        return
    
    print(f"总批次数: {total_batches}")
    
    while True:
        # 检查已完成的批次文件
        completed_files = []
        if os.path.exists('planet_data_practical'):
            for file in os.listdir('planet_data_practical'):
                if file.startswith('practical_batch_') and file.endswith('.csv'):
                    completed_files.append(file)
        
        completed_count = len(completed_files)
        progress_percentage = completed_count / total_batches * 100
        
        # 检查结果文件
        results_exist = os.path.exists('practical_download_results.json')
        
        print(f"\r进度: {completed_count}/{total_batches} ({progress_percentage:.1f}%) | 结果文件: {'✅' if results_exist else '⏳'}", end='', flush=True)
        
        # 如果完成或有结果文件，退出监控
        if completed_count >= total_batches or results_exist:
            break
        
        time.sleep(10)  # 每10秒检查一次
    
    print(f"\n\n📈 下载状态检查:")
    
    if results_exist:
        try:
            with open('practical_download_results.json', 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            summary = results['summary']
            print(f"✅ 下载完成！")
            print(f"   成功批次: {summary['successful_batches']}/{summary['total_batches']}")
            print(f"   总数据点: {summary['total_points']:,}")
            print(f"   完成时间: {summary['download_time']}")
            
        except Exception as e:
            print(f"❌ 读取结果文件失败: {e}")
    else:
        print(f"📁 批次文件: {completed_count}/{total_batches}")
        if completed_count < total_batches:
            print(f"⏳ 下载仍在进行中...")
        else:
            print(f"✅ 所有批次文件已生成，等待结果汇总...")

if __name__ == "__main__":
    monitor_practical_download()
