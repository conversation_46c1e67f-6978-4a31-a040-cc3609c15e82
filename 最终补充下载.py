#!/usr/bin/env python3
"""
最终补充下载：
1. 找出并补充缺失的最后2个数据点
2. 延续数据到2025年1月31日
"""

import os
import pandas as pd
import numpy as np
import warnings
import ssl
import urllib3
import time
from datetime import datetime, timedelta

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

class FinalDataCompleter:
    def __init__(self):
        """初始化最终补充器"""
        self.step_size = '596m'  # 596分钟
        self.target_interval_hours = 596 / 60  # 9.9333小时
        self.current_file = 'planet_data/木星_完整公转周期_596分钟间隔_轨道数据_完整版.csv'
        self.final_file = 'planet_data/木星_完整数据_含2025年1月.csv'
        
    def find_missing_points(self):
        """找出缺失的2个数据点"""
        print("🔍 分析缺失的最后2个数据点")
        print("=" * 50)
        
        # 读取当前数据
        df = pd.read_csv(self.current_file)
        df['datetime'] = pd.to_datetime(df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
        df = df.sort_values('datetime').reset_index(drop=True)
        
        print(f"当前数据点: {len(df):,}")
        
        # 计算时间间隔
        time_diffs = df['datetime'].diff().dropna()
        time_diffs_hours = time_diffs.dt.total_seconds() / 3600
        
        # 找出异常大的间隔（可能的缺失点）
        tolerance = 2.0  # 2小时容差
        large_gaps = time_diffs_hours[time_diffs_hours > self.target_interval_hours + tolerance]
        
        missing_periods = []
        
        print(f"发现 {len(large_gaps)} 个可能的缺失间隙:")
        
        for i, gap_hours in enumerate(large_gaps):
            gap_index = time_diffs_hours[time_diffs_hours == gap_hours].index[0]
            
            time_before = df.loc[gap_index-1, 'datetime']
            time_after = df.loc[gap_index, 'datetime']
            
            # 计算应该插入的点数
            expected_points = max(0, int(gap_hours / self.target_interval_hours) - 1)
            
            if expected_points > 0:
                print(f"  间隙 {i+1}: {time_before} 到 {time_after}")
                print(f"    间隔: {gap_hours:.2f} 小时")
                print(f"    缺失点数: {expected_points}")
                
                # 计算插入点的时间
                start_time = time_before + timedelta(hours=self.target_interval_hours)
                end_time = time_after - timedelta(hours=self.target_interval_hours)
                
                missing_periods.append({
                    'start': start_time.strftime('%Y-%m-%d %H:%M'),
                    'end': end_time.strftime('%Y-%m-%d %H:%M'),
                    'start_date': start_time.strftime('%Y-%m-%d'),
                    'end_date': end_time.strftime('%Y-%m-%d'),
                    'expected_points': expected_points
                })
        
        return missing_periods
    
    def download_missing_data(self, periods):
        """下载缺失的数据点"""
        print(f"\n📥 下载缺失数据点")
        print("=" * 50)
        
        all_new_data = []
        
        for i, period in enumerate(periods, 1):
            try:
                print(f"任务 {i}: {period['start_date']} 到 {period['end_date']}")
                
                from astroquery.jplhorizons import Horizons
                
                jupiter = Horizons(
                    id='599',
                    location='@sun',
                    epochs={
                        'start': period['start_date'],
                        'stop': period['end_date'],
                        'step': self.step_size
                    }
                )
                
                vectors = jupiter.vectors()
                df = vectors.to_pandas()
                processed_df = self.process_data(df)
                
                print(f"✅ 获取 {len(processed_df)} 个数据点")
                all_new_data.append(processed_df)
                
                time.sleep(1)  # 短暂延迟
                
            except Exception as e:
                print(f"❌ 任务 {i} 失败: {e}")
        
        return all_new_data
    
    def download_extension_data(self):
        """下载延续到2025年1月31日的数据"""
        print(f"\n📈 下载延续数据到2025年1月31日")
        print("=" * 50)
        
        try:
            from astroquery.jplhorizons import Horizons
            
            # 从2024年12月31日到2025年1月31日
            start_date = '2024-12-31'
            end_date = '2025-01-31'
            
            print(f"时间范围: {start_date} 到 {end_date}")
            
            jupiter = Horizons(
                id='599',
                location='@sun',
                epochs={
                    'start': start_date,
                    'stop': end_date,
                    'step': self.step_size
                }
            )
            
            vectors = jupiter.vectors()
            df = vectors.to_pandas()
            processed_df = self.process_data(df)
            
            print(f"✅ 获取延续数据 {len(processed_df)} 个数据点")
            return processed_df
            
        except Exception as e:
            print(f"❌ 延续数据下载失败: {e}")
            return None
    
    def process_data(self, df):
        """处理原始数据"""
        au_to_km = 149597870.7
        day_to_sec = 86400
        light_speed_au_per_sec = 1.0 / 499.004783836
        
        processed_df = pd.DataFrame()
        
        processed_df['julian_date'] = df['datetime_jd']
        processed_df['date'] = df['datetime_str']
        processed_df['x_position_au'] = df['x']
        processed_df['y_position_au'] = df['y']
        processed_df['z_position_au'] = df['z']
        
        processed_df['x_velocity_km_s'] = df['vx'] * au_to_km / day_to_sec
        processed_df['y_velocity_km_s'] = df['vy'] * au_to_km / day_to_sec
        processed_df['z_velocity_km_s'] = df['vz'] * au_to_km / day_to_sec
        
        processed_df['distance_au'] = df['range']
        processed_df['distance_km'] = df['range'] * au_to_km
        processed_df['light_time_sec'] = df['range'] / light_speed_au_per_sec
        processed_df['range_rate_km_s'] = df['range_rate'] * au_to_km / day_to_sec
        
        processed_df['speed_km_s'] = np.sqrt(
            processed_df['x_velocity_km_s']**2 + 
            processed_df['y_velocity_km_s']**2 + 
            processed_df['z_velocity_km_s']**2
        )
        
        return processed_df
    
    def merge_all_data(self, missing_data_list, extension_data):
        """合并所有数据"""
        print(f"\n🔄 合并所有数据")
        print("=" * 50)
        
        # 读取当前数据
        current_df = pd.read_csv(self.current_file)
        print(f"当前数据: {len(current_df):,} 个点")
        
        all_dataframes = [current_df]
        
        # 添加缺失数据
        if missing_data_list:
            for i, df in enumerate(missing_data_list):
                print(f"缺失数据 {i+1}: {len(df)} 个点")
                all_dataframes.append(df)
        
        # 添加延续数据
        if extension_data is not None:
            print(f"延续数据: {len(extension_data)} 个点")
            all_dataframes.append(extension_data)
        
        # 合并所有数据
        final_df = pd.concat(all_dataframes, ignore_index=True)
        
        # 按时间排序
        final_df['datetime'] = pd.to_datetime(final_df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
        final_df = final_df.sort_values('datetime').reset_index(drop=True)
        final_df = final_df.drop('datetime', axis=1)
        
        # 去重
        original_length = len(final_df)
        final_df = final_df.drop_duplicates(subset=['julian_date'], keep='first')
        duplicates_removed = original_length - len(final_df)
        
        if duplicates_removed > 0:
            print(f"移除重复数据: {duplicates_removed} 个")
        
        print(f"最终数据: {len(final_df):,} 个点")
        
        # 保存最终数据
        final_df.to_csv(self.final_file, index=False, encoding='utf-8-sig')
        print(f"✅ 最终数据已保存: {self.final_file}")
        
        return final_df
    
    def calculate_final_stats(self, df):
        """计算最终统计"""
        print(f"\n📊 最终数据统计")
        print("=" * 50)
        
        df['datetime'] = pd.to_datetime(df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
        
        start_time = df['datetime'].iloc[0]
        end_time = df['datetime'].iloc[-1]
        total_duration = (end_time - start_time).total_seconds() / (24 * 3600)
        
        print(f"📅 时间信息:")
        print(f"  开始时间: {start_time}")
        print(f"  结束时间: {end_time}")
        print(f"  总时长: {total_duration:.1f} 天 ({total_duration/365.25:.2f} 年)")
        
        # 计算覆盖率
        theoretical_points = total_duration * 24 / self.target_interval_hours
        coverage_rate = len(df) / theoretical_points * 100
        
        print(f"\n📈 覆盖率:")
        print(f"  理论数据点: {theoretical_points:.0f}")
        print(f"  实际数据点: {len(df):,}")
        print(f"  覆盖率: {coverage_rate:.3f}%")
        
        # 轨道数据统计
        print(f"\n🌌 轨道数据:")
        print(f"  平均距离: {df['distance_au'].mean():.4f} AU")
        print(f"  距离范围: {df['distance_au'].min():.4f} - {df['distance_au'].max():.4f} AU")
        print(f"  平均速度: {df['speed_km_s'].mean():.2f} km/s")
        print(f"  速度范围: {df['speed_km_s'].min():.2f} - {df['speed_km_s'].max():.2f} km/s")
        
        if coverage_rate >= 99.9:
            print(f"\n🌟 数据质量：完美！覆盖率接近100%")
        elif coverage_rate >= 99.5:
            print(f"\n✅ 数据质量：优秀！")
        else:
            print(f"\n📈 数据质量：良好！")
    
    def complete_data(self):
        """执行完整的数据补充"""
        print("🪐 木星数据最终补充")
        print("=" * 60)
        print("任务：1) 补充缺失的2个数据点  2) 延续到2025年1月31日")
        
        # 1. 找出缺失点
        missing_periods = self.find_missing_points()
        
        # 2. 下载缺失数据
        missing_data = []
        if missing_periods:
            missing_data = self.download_missing_data(missing_periods)
        else:
            print("✅ 未发现明显的缺失数据点")
        
        # 3. 下载延续数据
        extension_data = self.download_extension_data()
        
        # 4. 合并所有数据
        if missing_data or extension_data:
            final_df = self.merge_all_data(missing_data, extension_data)
            
            # 5. 计算最终统计
            self.calculate_final_stats(final_df)
            
            print(f"\n🎉 数据补充完成！")
            print(f"现在您拥有了完美的木星轨道数据，覆盖率接近100%，并延续到2025年1月！")
            
            return True
        else:
            print(f"\n❌ 未获取到新数据")
            return False

def main():
    """主函数"""
    completer = FinalDataCompleter()
    completer.complete_data()

if __name__ == "__main__":
    main()
