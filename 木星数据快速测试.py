#!/usr/bin/env python3
"""
木星数据快速测试
先下载一个月的数据验证格式和质量
"""

import os
import pandas as pd
import numpy as np
import warnings
import ssl
import urllib3

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

def download_jupiter_month():
    """下载木星一个月数据"""
    print("🪐 木星数据快速测试")
    print("=" * 50)
    print("下载2024年12月的木星数据")
    print("步长: 596分钟 (≈9.93小时，接近木星日9.925小时)")
    
    try:
        from astroquery.jplhorizons import Horizons
        
        print("\n正在连接JPL HORIZONS...")
        
        jupiter = Horizons(
            id='599',  # 木星
            location='@sun',  # 日心坐标系
            epochs={
                'start': '2024-12-01',
                'stop': '2024-12-31',
                'step': '596m'  # 596分钟
            }
        )
        
        print("正在查询数据...")
        vectors = jupiter.vectors()
        
        print(f"✅ 成功获取 {len(vectors)} 个数据点")
        
        # 转换为DataFrame
        df = vectors.to_pandas()
        
        # 处理数据
        processed_df = process_jupiter_data(df)
        
        # 保存数据
        output_file = 'planet_data/木星_2024年12月_596分钟间隔_测试数据.csv'
        os.makedirs('planet_data', exist_ok=True)
        processed_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"📁 数据已保存: {output_file}")
        
        # 显示统计
        show_statistics(processed_df)
        
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def process_jupiter_data(df):
    """处理木星数据"""
    print("正在处理数据...")
    
    # 单位转换常数
    au_to_km = 149597870.7
    day_to_sec = 86400
    light_speed_au_per_sec = 1.0 / 499.004783836
    
    processed_df = pd.DataFrame()
    
    # 基本列
    processed_df['julian_date'] = df['datetime_jd']
    processed_df['date'] = df['datetime_str']
    processed_df['x_position_au'] = df['x']
    processed_df['y_position_au'] = df['y']
    processed_df['z_position_au'] = df['z']
    
    # 速度转换 (AU/day -> km/s)
    processed_df['x_velocity_km_s'] = df['vx'] * au_to_km / day_to_sec
    processed_df['y_velocity_km_s'] = df['vy'] * au_to_km / day_to_sec
    processed_df['z_velocity_km_s'] = df['vz'] * au_to_km / day_to_sec
    
    # 距离
    processed_df['distance_au'] = df['range']
    processed_df['distance_km'] = df['range'] * au_to_km
    
    # 光时 (秒)
    processed_df['light_time_sec'] = df['range'] / light_speed_au_per_sec
    
    # 距离变化率 (AU/day -> km/s)
    processed_df['range_rate_km_s'] = df['range_rate'] * au_to_km / day_to_sec
    
    # 轨道速度 (km/s)
    processed_df['speed_km_s'] = np.sqrt(
        processed_df['x_velocity_km_s']**2 + 
        processed_df['y_velocity_km_s']**2 + 
        processed_df['z_velocity_km_s']**2
    )
    
    return processed_df

def show_statistics(df):
    """显示数据统计"""
    print(f"\n📊 数据统计:")
    print(f"数据点数: {len(df)}")
    print(f"时间跨度: {df['date'].iloc[0]} 到 {df['date'].iloc[-1]}")
    
    print(f"\n距离统计 (AU):")
    print(f"  平均距离: {df['distance_au'].mean():.4f}")
    print(f"  最近距离: {df['distance_au'].min():.4f}")
    print(f"  最远距离: {df['distance_au'].max():.4f}")
    print(f"  距离变化: {df['distance_au'].max() - df['distance_au'].min():.4f}")
    
    print(f"\n速度统计 (km/s):")
    print(f"  平均速度: {df['speed_km_s'].mean():.2f}")
    print(f"  最高速度: {df['speed_km_s'].max():.2f}")
    print(f"  最低速度: {df['speed_km_s'].min():.2f}")
    print(f"  速度变化: {df['speed_km_s'].max() - df['speed_km_s'].min():.2f}")
    
    print(f"\n光时统计:")
    print(f"  平均光时: {df['light_time_sec'].mean()/60:.1f} 分钟")
    print(f"  最短光时: {df['light_time_sec'].min()/60:.1f} 分钟")
    print(f"  最长光时: {df['light_time_sec'].max()/60:.1f} 分钟")
    
    # 计算时间间隔
    if len(df) > 1:
        time_diffs = df['julian_date'].diff().dropna() * 24  # 转换为小时
        avg_interval = time_diffs.mean()
        print(f"\n时间间隔统计:")
        print(f"  平均间隔: {avg_interval:.4f} 小时")
        print(f"  目标木星日: 9.9250 小时")
        print(f"  间隔误差: {abs(avg_interval - 9.925):.4f} 小时")
        print(f"  相对误差: {abs(avg_interval - 9.925)/9.925*100:.2f}%")
    
    # 显示前几行数据
    print(f"\n📋 前5行数据:")
    print(df.head())

def main():
    """主函数"""
    success = download_jupiter_month()
    
    if success:
        print(f"\n🎉 木星数据快速测试成功！")
        print("数据格式和质量验证通过，可以继续下载完整公转周期数据。")
    else:
        print(f"\n❌ 测试失败，请检查网络连接。")

if __name__ == "__main__":
    main()
