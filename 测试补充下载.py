#!/usr/bin/env python3
"""
测试补充下载一个小的缺失时间段
"""

import os
import pandas as pd
import numpy as np
import warnings
import ssl
import urllib3

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

def test_fill_gap():
    """测试填补一个小的数据缺口"""
    print("🧪 测试补充下载")
    print("=" * 50)
    
    # 测试下载2013年3月21日到3月28日的数据
    test_start = '2013-03-21'
    test_end = '2013-03-28'
    
    print(f"测试时间段: {test_start} 到 {test_end}")
    print(f"步长: 596分钟")
    
    try:
        from astroquery.jplhorizons import Horizons
        
        print("正在连接JPL HORIZONS...")
        
        jupiter = Horizons(
            id='599',  # 木星
            location='@sun',
            epochs={
                'start': test_start,
                'stop': test_end,
                'step': '596m'
            }
        )
        
        print("正在查询数据...")
        vectors = jupiter.vectors()
        
        print(f"✅ 成功获取 {len(vectors)} 个数据点")
        
        # 转换为DataFrame
        df = vectors.to_pandas()
        
        # 处理数据
        processed_df = process_jupiter_data(df)
        
        # 保存测试数据
        test_file = 'planet_data/木星_补充测试_2013年3月.csv'
        os.makedirs('planet_data', exist_ok=True)
        processed_df.to_csv(test_file, index=False, encoding='utf-8-sig')
        
        print(f"📁 测试数据已保存: {test_file}")
        print(f"数据点数: {len(processed_df)}")
        
        # 显示数据预览
        print(f"\n📋 数据预览:")
        print(f"时间范围: {processed_df['date'].iloc[0]} 到 {processed_df['date'].iloc[-1]}")
        print(f"平均距离: {processed_df['distance_au'].mean():.4f} AU")
        print(f"平均速度: {processed_df['speed_km_s'].mean():.2f} km/s")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def process_jupiter_data(df):
    """处理木星数据"""
    # 单位转换常数
    au_to_km = 149597870.7
    day_to_sec = 86400
    light_speed_au_per_sec = 1.0 / 499.004783836
    
    processed_df = pd.DataFrame()
    
    # 基本列
    processed_df['julian_date'] = df['datetime_jd']
    processed_df['date'] = df['datetime_str']
    processed_df['x_position_au'] = df['x']
    processed_df['y_position_au'] = df['y']
    processed_df['z_position_au'] = df['z']
    
    # 速度转换 (AU/day -> km/s)
    processed_df['x_velocity_km_s'] = df['vx'] * au_to_km / day_to_sec
    processed_df['y_velocity_km_s'] = df['vy'] * au_to_km / day_to_sec
    processed_df['z_velocity_km_s'] = df['vz'] * au_to_km / day_to_sec
    
    # 距离
    processed_df['distance_au'] = df['range']
    processed_df['distance_km'] = df['range'] * au_to_km
    
    # 光时 (秒)
    processed_df['light_time_sec'] = df['range'] / light_speed_au_per_sec
    
    # 距离变化率 (AU/day -> km/s)
    processed_df['range_rate_km_s'] = df['range_rate'] * au_to_km / day_to_sec
    
    # 轨道速度 (km/s)
    processed_df['speed_km_s'] = np.sqrt(
        processed_df['x_velocity_km_s']**2 + 
        processed_df['y_velocity_km_s']**2 + 
        processed_df['z_velocity_km_s']**2
    )
    
    return processed_df

def merge_test_data():
    """将测试数据与原始数据合并"""
    print(f"\n🔄 测试数据合并...")
    
    try:
        # 读取原始数据
        original_file = 'planet_data/木星_完整公转周期_596分钟间隔_轨道数据.csv'
        test_file = 'planet_data/木星_补充测试_2013年3月.csv'
        
        if not os.path.exists(test_file):
            print("❌ 测试数据文件不存在")
            return False
        
        original_df = pd.read_csv(original_file)
        test_df = pd.read_csv(test_file)
        
        print(f"原始数据: {len(original_df):,} 个点")
        print(f"测试数据: {len(test_df):,} 个点")
        
        # 合并数据
        combined_df = pd.concat([original_df, test_df], ignore_index=True)
        
        # 按时间排序
        combined_df['datetime'] = pd.to_datetime(combined_df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
        combined_df = combined_df.sort_values('datetime').reset_index(drop=True)
        combined_df = combined_df.drop('datetime', axis=1)
        
        # 去重
        original_length = len(combined_df)
        combined_df = combined_df.drop_duplicates(subset=['julian_date'], keep='first')
        duplicates_removed = original_length - len(combined_df)
        
        print(f"合并后: {len(combined_df):,} 个点")
        if duplicates_removed > 0:
            print(f"移除重复: {duplicates_removed} 个点")
        
        # 保存测试合并结果
        test_merged_file = 'planet_data/木星_测试合并_数据.csv'
        combined_df.to_csv(test_merged_file, index=False, encoding='utf-8-sig')
        
        print(f"✅ 测试合并完成: {test_merged_file}")
        print(f"数据增加: {len(combined_df) - len(original_df)} 个点")
        
        return True
        
    except Exception as e:
        print(f"❌ 合并失败: {e}")
        return False

def main():
    """主函数"""
    print("🪐 木星数据补充下载测试")
    print("=" * 60)
    
    # 测试1：下载缺失数据
    success1 = test_fill_gap()
    
    if success1:
        print(f"\n✅ 补充下载测试成功！")
        
        # 测试2：数据合并
        success2 = merge_test_data()
        
        if success2:
            print(f"\n🎉 所有测试通过！可以进行完整的补充下载。")
        else:
            print(f"\n⚠️ 数据合并测试失败")
    else:
        print(f"\n❌ 补充下载测试失败")

if __name__ == "__main__":
    main()
