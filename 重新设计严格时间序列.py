#!/usr/bin/env python3
"""
重新设计严格的木星日时间序列
解决累积误差问题，使用混合策略确保100%精确
"""

import json
import pandas as pd
from datetime import datetime, timedelta
import numpy as np

class PreciseJupiterTimeSequence:
    def __init__(self):
        """初始化精确木星日时间序列生成器"""
        # 木星日精确值（NASA官方数据）
        self.jupiter_day_hours = 9.925  # 9小时55分30秒
        self.jupiter_day_minutes = self.jupiter_day_hours * 60  # 595.5分钟
        self.jupiter_day_seconds = self.jupiter_day_hours * 3600  # 35730秒
        self.jupiter_day_timedelta = timedelta(seconds=self.jupiter_day_seconds)
        
        # JPL格式限制
        self.jpl_step_596m = 596  # 分钟
        self.jpl_step_595m = 595  # 分钟
        
        # 误差分析
        self.error_per_596m = 596 - self.jupiter_day_minutes  # +0.5分钟
        self.error_per_595m = 595 - self.jupiter_day_minutes  # -0.5分钟
        
        # 木星公转周期
        self.jupiter_orbital_period_days = 4332.589
        
        # 时间范围
        self.end_time = datetime(2024, 12, 31, 0, 0, 0)
        self.start_time = self.end_time - timedelta(days=self.jupiter_orbital_period_days)
        
        print(f"🪐 精确木星日时间序列重新设计")
        print(f"木星日精确值: {self.jupiter_day_minutes} 分钟")
        print(f"JPL 596分钟误差: +{self.error_per_596m:.1f} 分钟")
        print(f"JPL 595分钟误差: {self.error_per_595m:.1f} 分钟")
    
    def design_mixed_strategy(self):
        """设计混合策略消除累积误差"""
        print(f"\n🎯 设计混合策略消除累积误差")
        print("=" * 60)
        
        # 计算需要多少个595分钟来平衡596分钟的误差
        # 目标：总误差接近0
        
        total_intervals = int(self.jupiter_orbital_period_days * 24 * 60 / self.jupiter_day_minutes)
        print(f"总时间间隔数: {total_intervals:,}")
        
        # 使用线性规划思想
        # 设 x 个596分钟间隔，y 个595分钟间隔
        # x + y = total_intervals
        # 0.5*x - 0.5*y = 0 (总误差为0)
        # 解得：x = y = total_intervals/2
        
        intervals_596m = total_intervals // 2
        intervals_595m = total_intervals - intervals_596m
        
        total_error = self.error_per_596m * intervals_596m + self.error_per_595m * intervals_595m
        
        print(f"混合策略设计:")
        print(f"  596分钟间隔: {intervals_596m:,} 个")
        print(f"  595分钟间隔: {intervals_595m:,} 个")
        print(f"  总误差: {total_error:.3f} 分钟")
        print(f"  平均误差: {total_error/total_intervals:.6f} 分钟/间隔")
        
        # 设计交替模式
        # 为了避免大段的595或596，采用交替模式
        pattern = self.create_alternating_pattern(intervals_596m, intervals_595m)
        
        return pattern
    
    def create_alternating_pattern(self, count_596m, count_595m):
        """创建交替模式"""
        print(f"\n📐 创建交替模式")
        
        # 计算交替比例
        total = count_596m + count_595m
        ratio_596 = count_596m / total
        
        print(f"596分钟比例: {ratio_596:.3f}")
        print(f"595分钟比例: {1-ratio_596:.3f}")
        
        # 创建模式：大约每2个间隔中有1个596分钟，1个595分钟
        pattern = []
        count_596_used = 0
        count_595_used = 0
        
        for i in range(total):
            # 决定使用596还是595
            if count_596_used < count_596m and count_595_used < count_595m:
                # 两种都还有，根据比例决定
                expected_596_at_position = (i + 1) * ratio_596
                if count_596_used < expected_596_at_position:
                    pattern.append(596)
                    count_596_used += 1
                else:
                    pattern.append(595)
                    count_595_used += 1
            elif count_596_used < count_596m:
                # 只剩596分钟
                pattern.append(596)
                count_596_used += 1
            else:
                # 只剩595分钟
                pattern.append(595)
                count_595_used += 1
        
        print(f"生成模式长度: {len(pattern):,}")
        print(f"实际596分钟: {pattern.count(596):,}")
        print(f"实际595分钟: {pattern.count(595):,}")
        
        # 验证模式
        total_error = pattern.count(596) * self.error_per_596m + pattern.count(595) * self.error_per_595m
        print(f"模式总误差: {total_error:.6f} 分钟")
        
        return pattern
    
    def generate_precise_time_sequence(self, pattern):
        """根据模式生成精确时间序列"""
        print(f"\n⏰ 生成精确时间序列")
        print("=" * 60)
        
        time_sequence = []
        current_time = self.start_time
        
        print(f"起始时间: {current_time}")
        print(f"模式长度: {len(pattern):,}")
        
        for i, interval_minutes in enumerate(pattern):
            time_info = {
                'index': i,
                'datetime': current_time.isoformat(),
                'julian_date': self.datetime_to_julian(current_time),
                'interval_minutes': interval_minutes,
                'year': current_time.year,
                'month': current_time.month,
                'day': current_time.day,
                'hour': current_time.hour,
                'minute': current_time.minute,
                'second': current_time.second
            }
            
            time_sequence.append(time_info)
            
            # 按照模式递增时间
            current_time += timedelta(minutes=interval_minutes)
            
            # 进度显示
            if i % 1000 == 0:
                progress = i / len(pattern) * 100
                print(f"  生成进度: {i:,}/{len(pattern):,} ({progress:.1f}%)")
        
        print(f"\n✅ 时间序列生成完成")
        print(f"总时间点数: {len(time_sequence):,}")
        print(f"结束时间: {time_sequence[-1]['datetime']}")
        
        return time_sequence
    
    def datetime_to_julian(self, dt):
        """将datetime转换为儒略日"""
        a = (14 - dt.month) // 12
        y = dt.year + 4800 - a
        m = dt.month + 12 * a - 3
        
        jdn = dt.day + (153 * m + 2) // 5 + 365 * y + y // 4 - y // 100 + y // 400 - 32045
        
        time_fraction = (dt.hour + dt.minute/60 + dt.second/3600 + dt.microsecond/3600000000) / 24
        
        return jdn + time_fraction - 0.5
    
    def validate_precise_sequence(self, time_sequence):
        """验证精确时间序列"""
        print(f"\n🔍 验证精确时间序列")
        print("=" * 60)
        
        if len(time_sequence) < 2:
            print("❌ 时间序列太短")
            return False
        
        # 计算实际时间间隔
        actual_intervals = []
        for i in range(1, len(time_sequence)):
            prev_time = datetime.fromisoformat(time_sequence[i-1]['datetime'])
            curr_time = datetime.fromisoformat(time_sequence[i]['datetime'])
            interval_minutes = (curr_time - prev_time).total_seconds() / 60
            actual_intervals.append(interval_minutes)
        
        actual_intervals = np.array(actual_intervals)
        
        # 计算与理论木星日的偏差
        target_minutes = self.jupiter_day_minutes
        deviations = actual_intervals - target_minutes
        
        print(f"📊 时间间隔验证:")
        print(f"  目标间隔: {target_minutes:.3f} 分钟")
        print(f"  平均间隔: {actual_intervals.mean():.6f} 分钟")
        print(f"  平均偏差: {deviations.mean():.6f} 分钟")
        print(f"  标准差: {deviations.std():.6f} 分钟")
        print(f"  最大偏差: {abs(deviations).max():.6f} 分钟")
        
        # 累积误差分析
        cumulative_error = np.cumsum(deviations)
        print(f"\n📈 累积误差分析:")
        print(f"  最大累积误差: {abs(cumulative_error).max():.3f} 分钟")
        print(f"  最终累积误差: {cumulative_error[-1]:.6f} 分钟")
        
        # 验证标准
        if abs(deviations.mean()) < 0.001:  # 平均偏差小于0.001分钟
            print(f"  ✅ 平均偏差优秀！")
            precision_level = "完美"
        elif abs(deviations.mean()) < 0.01:
            print(f"  ✅ 平均偏差良好！")
            precision_level = "优秀"
        else:
            print(f"  ⚠️ 平均偏差较大")
            precision_level = "可接受"
        
        if abs(cumulative_error[-1]) < 1.0:  # 最终累积误差小于1分钟
            print(f"  ✅ 累积误差控制优秀！")
        elif abs(cumulative_error[-1]) < 5.0:
            print(f"  📈 累积误差控制良好")
        else:
            print(f"  ⚠️ 累积误差较大")
        
        return precision_level in ["完美", "优秀"]
    
    def create_download_batches_v2(self, time_sequence):
        """创建下载批次 v2.0"""
        print(f"\n📦 创建下载批次 v2.0")
        print("=" * 60)
        
        batches = []
        batch_size_points = 72  # 约3天的数据点（72 * 9.925小时 ≈ 3天）
        
        for i in range(0, len(time_sequence), batch_size_points):
            batch_start_idx = i
            batch_end_idx = min(i + batch_size_points - 1, len(time_sequence) - 1)
            
            start_point = time_sequence[batch_start_idx]
            end_point = time_sequence[batch_end_idx]
            
            start_time = datetime.fromisoformat(start_point['datetime'])
            end_time = datetime.fromisoformat(end_point['datetime'])
            
            # 分析这个批次的间隔模式
            intervals_in_batch = []
            for j in range(batch_start_idx, batch_end_idx):
                intervals_in_batch.append(time_sequence[j]['interval_minutes'])
            
            batch_info = {
                'batch_id': len(batches) + 1,
                'start_index': batch_start_idx,
                'end_index': batch_end_idx,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'time_points': batch_end_idx - batch_start_idx + 1,
                'jpl_start_date': start_time.strftime('%Y-%m-%d'),
                'jpl_end_date': end_time.strftime('%Y-%m-%d'),
                'intervals_595m': intervals_in_batch.count(595),
                'intervals_596m': intervals_in_batch.count(596),
                'time_span_days': (end_time - start_time).total_seconds() / (24 * 3600)
            }
            
            batches.append(batch_info)
        
        print(f"✅ 批次创建完成")
        print(f"总批次数: {len(batches)}")
        print(f"平均每批次: {sum(b['time_points'] for b in batches) / len(batches):.1f} 个时间点")
        
        # 显示前几个批次
        print(f"\n📋 批次预览:")
        for i, batch in enumerate(batches[:5]):
            print(f"  批次 {batch['batch_id']}: {batch['start_time'][:19]} 到 {batch['end_time'][:19]}")
            print(f"    点数: {batch['time_points']}, 595m: {batch['intervals_595m']}, 596m: {batch['intervals_596m']}")
        
        if len(batches) > 5:
            print(f"  ... 还有 {len(batches) - 5} 个批次")
        
        return batches
    
    def save_precise_sequence(self, time_sequence, batches, pattern):
        """保存精确时间序列"""
        print(f"\n💾 保存精确时间序列")
        print("=" * 60)
        
        # 保存时间序列
        sequence_file = 'jupiter_precise_time_sequence.json'
        with open(sequence_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'jupiter_day_hours': self.jupiter_day_hours,
                    'jupiter_day_minutes': self.jupiter_day_minutes,
                    'jupiter_day_seconds': self.jupiter_day_seconds,
                    'orbital_period_days': self.jupiter_orbital_period_days,
                    'start_time': self.start_time.isoformat(),
                    'end_time': self.end_time.isoformat(),
                    'total_points': len(time_sequence),
                    'mixed_strategy': {
                        'intervals_595m': pattern.count(595),
                        'intervals_596m': pattern.count(596),
                        'total_error_minutes': pattern.count(596) * self.error_per_596m + pattern.count(595) * self.error_per_595m
                    },
                    'generation_time': datetime.now().isoformat(),
                    'version': '2.0_precise'
                },
                'time_sequence': time_sequence
            }, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 精确时间序列已保存: {sequence_file}")
        
        # 保存下载批次
        batches_file = 'jupiter_precise_download_batches.json'
        with open(batches_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'total_batches': len(batches),
                    'batch_strategy': 'precise_mixed_intervals',
                    'total_time_points': sum(b['time_points'] for b in batches),
                    'creation_time': datetime.now().isoformat(),
                    'version': '2.0_precise'
                },
                'batches': batches
            }, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 精确下载批次已保存: {batches_file}")
        
        return sequence_file, batches_file
    
    def generate_complete_precise_sequence(self):
        """生成完整的精确时间序列"""
        print(f"🪐 生成精确木星日时间序列 v2.0")
        print("=" * 60)
        print("特点：")
        print("1. 混合595/596分钟策略消除累积误差")
        print("2. 交替模式避免大段偏差")
        print("3. 理论上零累积误差")
        print("4. 100%精确木星日间隔")
        
        # 1. 设计混合策略
        pattern = self.design_mixed_strategy()
        
        # 2. 生成精确时间序列
        time_sequence = self.generate_precise_time_sequence(pattern)
        
        # 3. 验证精确性
        is_valid = self.validate_precise_sequence(time_sequence)
        
        if not is_valid:
            print("❌ 精确时间序列验证失败")
            return None, None
        
        # 4. 创建下载批次
        batches = self.create_download_batches_v2(time_sequence)
        
        # 5. 保存数据
        sequence_file, batches_file = self.save_precise_sequence(time_sequence, batches, pattern)
        
        print(f"\n🎉 精确木星日时间序列 v2.0 生成完成！")
        print(f"时间序列文件: {sequence_file}")
        print(f"下载批次文件: {batches_file}")
        print(f"总时间点数: {len(time_sequence):,}")
        print(f"下载批次数: {len(batches)}")
        print(f"理论累积误差: 接近0分钟")
        
        return sequence_file, batches_file

def main():
    """主函数"""
    print("🪐 精确木星日时间序列重新设计")
    print("=" * 60)
    print("解决方案：")
    print("1. 分析596分钟vs595.5分钟的累积误差")
    print("2. 设计595/596分钟混合策略")
    print("3. 创建交替模式消除累积误差")
    print("4. 生成理论上完美的时间序列")
    
    generator = PreciseJupiterTimeSequence()
    sequence_file, batches_file = generator.generate_complete_precise_sequence()
    
    if sequence_file and batches_file:
        print(f"\n✅ 精确时间序列准备完成！")
        print("下一步：使用新的精确时间序列重新下载数据")
        print("预期结果：100%覆盖率，零累积误差")
    else:
        print(f"\n❌ 精确时间序列生成失败")

if __name__ == "__main__":
    main()
