#!/usr/bin/env python3
"""
补充地球2023年数据的缺失列
将下载的CSV数据补充为与地球_2024_1天_处理后数据.csv相同的列结构
"""

import pandas as pd
import numpy as np

def add_missing_columns():
    """补充缺失的列：light_time_sec 和 range_rate_km_s"""
    
    print("正在读取地球2023年数据...")
    
    # 读取下载的2023年数据
    df_2023 = pd.read_csv('planet_data/地球_2023_data.csv')
    
    print(f"原始数据形状: {df_2023.shape}")
    print(f"原始列: {list(df_2023.columns)}")
    
    # 计算缺失的列
    print("\n正在计算缺失的列...")
    
    # 1. 计算 light_time_sec (光时，单位：秒)
    # 光时 = 距离(AU) / 光速(AU/s)
    # 光速 ≈ 499.004783836 秒/AU (光从太阳到地球的时间)
    light_speed_au_per_sec = 1.0 / 499.004783836  # AU/秒
    df_2023['light_time_sec'] = df_2023['distance_au'] / light_speed_au_per_sec
    
    # 2. 计算 range_rate_km_s (距离变化率，单位：km/s)
    # range_rate_au_day 转换为 km/s
    # 1 AU = 149,597,870.7 km
    # 1 day = 86400 seconds
    au_to_km = 149597870.7
    day_to_sec = 86400
    df_2023['range_rate_km_s'] = df_2023['range_rate_au_day'] * au_to_km / day_to_sec
    
    # 3. 添加其他需要的列以匹配2024年数据格式
    
    # 计算距离(km)
    df_2023['distance_km'] = df_2023['distance_au'] * au_to_km
    
    # 转换速度单位从AU/day到km/s
    df_2023['x_velocity_km_s'] = df_2023['x_velocity_au_day'] * au_to_km / day_to_sec
    df_2023['y_velocity_km_s'] = df_2023['y_velocity_au_day'] * au_to_km / day_to_sec
    df_2023['z_velocity_km_s'] = df_2023['z_velocity_au_day'] * au_to_km / day_to_sec
    
    # 重新排列列的顺序以匹配2024年数据
    columns_order = [
        'julian_date', 'date', 
        'x_position_au', 'y_position_au', 'z_position_au',
        'x_velocity_km_s', 'y_velocity_km_s', 'z_velocity_km_s',
        'distance_au', 'distance_km', 'light_time_sec', 'range_rate_km_s', 'speed_km_s'
    ]
    
    df_2023_processed = df_2023[columns_order]
    
    print(f"\n处理后数据形状: {df_2023_processed.shape}")
    print(f"处理后列: {list(df_2023_processed.columns)}")
    
    # 保存处理后的数据
    output_file = 'planet_data/地球_2023_1天_处理后数据.csv'
    df_2023_processed.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n✅ 处理后的数据已保存到: {output_file}")
    
    # 显示数据统计
    print(f"\n📊 数据统计:")
    print(f"总天数: {len(df_2023_processed)}")
    print(f"平均距离: {df_2023_processed['distance_au'].mean():.6f} AU")
    print(f"平均光时: {df_2023_processed['light_time_sec'].mean():.2f} 秒")
    print(f"平均距离变化率: {df_2023_processed['range_rate_km_s'].mean():.4f} km/s")
    print(f"平均轨道速度: {df_2023_processed['speed_km_s'].mean():.2f} km/s")
    
    # 显示前几行数据
    print(f"\n📋 前5行数据:")
    print(df_2023_processed.head())
    
    return df_2023_processed

def compare_with_2024_data():
    """与2024年数据进行对比验证"""
    print("\n" + "="*60)
    print("与2024年数据进行对比验证")
    print("="*60)
    
    try:
        # 读取2024年数据
        df_2024 = pd.read_csv('planet_data/地球_2024_1天_处理后数据.csv')
        df_2023 = pd.read_csv('planet_data/地球_2023_1天_处理后数据.csv')
        
        print(f"2024年数据列: {list(df_2024.columns)}")
        print(f"2023年数据列: {list(df_2023.columns)}")
        
        # 检查列是否匹配
        if list(df_2024.columns) == list(df_2023.columns):
            print("✅ 列结构完全匹配！")
        else:
            print("❌ 列结构不匹配")
            print(f"2024年独有列: {set(df_2024.columns) - set(df_2023.columns)}")
            print(f"2023年独有列: {set(df_2023.columns) - set(df_2024.columns)}")
        
        # 比较数据范围
        print(f"\n📊 数据范围对比:")
        print(f"2024年数据天数: {len(df_2024)}")
        print(f"2023年数据天数: {len(df_2023)}")
        
        print(f"\n距离对比 (AU):")
        print(f"2024年平均距离: {df_2024['distance_au'].mean():.6f}")
        print(f"2023年平均距离: {df_2023['distance_au'].mean():.6f}")
        
        print(f"\n速度对比 (km/s):")
        print(f"2024年平均速度: {df_2024['speed_km_s'].mean():.2f}")
        print(f"2023年平均速度: {df_2023['speed_km_s'].mean():.2f}")
        
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")

def main():
    """主函数"""
    print("🌍 地球轨道数据列补充工具")
    print("=" * 60)
    
    # 补充缺失列
    processed_data = add_missing_columns()
    
    # 与2024年数据对比
    compare_with_2024_data()
    
    print("\n✅ 数据处理完成！")

if __name__ == "__main__":
    main()
