#!/usr/bin/env python3
"""
生成严格的木星日时间序列
确保每个时间点都精确按照木星日间隔递增
"""

import json
import pandas as pd
from datetime import datetime, timedelta
import numpy as np

class JupiterDayTimeSequence:
    def __init__(self):
        """初始化木星日时间序列生成器"""
        # 木星日精确值（NASA官方数据）
        self.jupiter_day_hours = 9.925  # 9小时55分30秒
        self.jupiter_day_seconds = self.jupiter_day_hours * 3600  # 35730秒
        self.jupiter_day_timedelta = timedelta(seconds=self.jupiter_day_seconds)
        
        # 木星公转周期
        self.jupiter_orbital_period_days = 4332.589  # 天
        
        # 时间范围设定
        self.end_time = datetime(2024, 12, 31, 0, 0, 0)  # 结束时间
        self.start_time = self.end_time - timedelta(days=self.jupiter_orbital_period_days)
        
        print(f"🪐 木星日时间序列生成器初始化")
        print(f"木星日长度: {self.jupiter_day_hours} 小时 = {self.jupiter_day_seconds} 秒")
        print(f"时间范围: {self.start_time} 到 {self.end_time}")
        print(f"总时长: {self.jupiter_orbital_period_days:.3f} 天")
    
    def generate_time_sequence(self):
        """生成严格的木星日时间序列"""
        print(f"\n⏰ 生成严格木星日时间序列")
        print("=" * 60)
        
        time_sequence = []
        current_time = self.start_time
        sequence_index = 0
        
        print(f"起始时间: {current_time}")
        print(f"时间步长: {self.jupiter_day_timedelta}")
        
        # 生成时间序列直到结束时间
        while current_time <= self.end_time:
            time_info = {
                'index': sequence_index,
                'datetime': current_time.isoformat(),
                'julian_date': self.datetime_to_julian(current_time),
                'year': current_time.year,
                'month': current_time.month,
                'day': current_time.day,
                'hour': current_time.hour,
                'minute': current_time.minute,
                'second': current_time.second,
                'microsecond': current_time.microsecond
            }
            
            time_sequence.append(time_info)
            
            # 严格按照木星日间隔递增
            current_time += self.jupiter_day_timedelta
            sequence_index += 1
            
            # 进度显示
            if sequence_index % 1000 == 0:
                progress = (current_time - self.start_time).total_seconds() / (self.end_time - self.start_time).total_seconds() * 100
                print(f"  生成进度: {sequence_index:,} 个时间点 ({progress:.1f}%)")
        
        print(f"\n✅ 时间序列生成完成")
        print(f"总时间点数: {len(time_sequence):,}")
        print(f"结束时间: {time_sequence[-1]['datetime']}")
        
        return time_sequence
    
    def datetime_to_julian(self, dt):
        """将datetime转换为儒略日"""
        # 儒略日计算公式
        a = (14 - dt.month) // 12
        y = dt.year + 4800 - a
        m = dt.month + 12 * a - 3
        
        jdn = dt.day + (153 * m + 2) // 5 + 365 * y + y // 4 - y // 100 + y // 400 - 32045
        
        # 加上时间部分
        time_fraction = (dt.hour + dt.minute/60 + dt.second/3600 + dt.microsecond/3600000000) / 24
        
        return jdn + time_fraction - 0.5
    
    def validate_time_sequence(self, time_sequence):
        """验证时间序列的严格性"""
        print(f"\n🔍 验证时间序列严格性")
        print("=" * 60)
        
        if len(time_sequence) < 2:
            print("❌ 时间序列太短，无法验证")
            return False
        
        # 检查时间间隔的一致性
        intervals = []
        for i in range(1, len(time_sequence)):
            prev_time = datetime.fromisoformat(time_sequence[i-1]['datetime'])
            curr_time = datetime.fromisoformat(time_sequence[i]['datetime'])
            interval = (curr_time - prev_time).total_seconds()
            intervals.append(interval)
        
        intervals = np.array(intervals)
        
        print(f"📊 间隔统计:")
        print(f"  目标间隔: {self.jupiter_day_seconds} 秒")
        print(f"  平均间隔: {intervals.mean():.6f} 秒")
        print(f"  标准差: {intervals.std():.6f} 秒")
        print(f"  最小间隔: {intervals.min():.6f} 秒")
        print(f"  最大间隔: {intervals.max():.6f} 秒")
        
        # 检查精确性
        max_deviation = np.abs(intervals - self.jupiter_day_seconds).max()
        print(f"  最大偏差: {max_deviation:.6f} 秒")
        
        # 验证标准
        if max_deviation < 0.001:  # 1毫秒精度
            print(f"  ✅ 时间序列完美！精度达到毫秒级")
            return True
        elif max_deviation < 1.0:  # 1秒精度
            print(f"  ✅ 时间序列优秀！精度达到秒级")
            return True
        else:
            print(f"  ❌ 时间序列存在偏差")
            return False
    
    def create_download_batches(self, time_sequence, batch_size_days=30):
        """创建下载批次，确保时间连续性"""
        print(f"\n📦 创建下载批次")
        print("=" * 60)
        
        batches = []
        current_batch = []
        batch_start_time = None
        batch_end_time = None
        
        for i, time_point in enumerate(time_sequence):
            current_time = datetime.fromisoformat(time_point['datetime'])
            
            # 开始新批次
            if batch_start_time is None:
                batch_start_time = current_time
                current_batch = [time_point]
            else:
                # 检查是否需要结束当前批次
                time_span = (current_time - batch_start_time).total_seconds() / (24 * 3600)
                
                if time_span >= batch_size_days or i == len(time_sequence) - 1:
                    # 结束当前批次
                    batch_end_time = current_time
                    
                    batch_info = {
                        'batch_id': len(batches) + 1,
                        'start_time': batch_start_time.isoformat(),
                        'end_time': batch_end_time.isoformat(),
                        'start_index': current_batch[0]['index'],
                        'end_index': time_point['index'],
                        'time_points': len(current_batch) + (1 if i == len(time_sequence) - 1 else 0),
                        'time_span_days': time_span,
                        'jpl_start_date': batch_start_time.strftime('%Y-%m-%d'),
                        'jpl_end_date': batch_end_time.strftime('%Y-%m-%d')
                    }
                    
                    batches.append(batch_info)
                    
                    # 开始新批次（如果不是最后一个点）
                    if i < len(time_sequence) - 1:
                        batch_start_time = current_time
                        current_batch = [time_point]
                    
                else:
                    # 继续当前批次
                    current_batch.append(time_point)
        
        print(f"✅ 批次创建完成")
        print(f"总批次数: {len(batches)}")
        print(f"平均每批次: {sum(b['time_points'] for b in batches) / len(batches):.1f} 个时间点")
        
        # 显示前几个批次
        print(f"\n📋 批次预览:")
        for i, batch in enumerate(batches[:5]):
            print(f"  批次 {batch['batch_id']}: {batch['start_time'][:19]} 到 {batch['end_time'][:19]} ({batch['time_points']} 个点)")
        
        if len(batches) > 5:
            print(f"  ... 还有 {len(batches) - 5} 个批次")
        
        return batches
    
    def save_time_sequence(self, time_sequence, batches):
        """保存时间序列和批次信息"""
        print(f"\n💾 保存时间序列数据")
        print("=" * 60)
        
        # 保存完整时间序列
        sequence_file = 'jupiter_time_sequence.json'
        with open(sequence_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'jupiter_day_hours': self.jupiter_day_hours,
                    'jupiter_day_seconds': self.jupiter_day_seconds,
                    'orbital_period_days': self.jupiter_orbital_period_days,
                    'start_time': self.start_time.isoformat(),
                    'end_time': self.end_time.isoformat(),
                    'total_points': len(time_sequence),
                    'generation_time': datetime.now().isoformat()
                },
                'time_sequence': time_sequence
            }, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 时间序列已保存: {sequence_file}")
        print(f"   包含 {len(time_sequence):,} 个时间点")
        
        # 保存下载批次
        batches_file = 'jupiter_download_batches.json'
        with open(batches_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'total_batches': len(batches),
                    'batch_size_days': 30,
                    'total_time_points': sum(b['time_points'] for b in batches),
                    'creation_time': datetime.now().isoformat()
                },
                'batches': batches
            }, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 下载批次已保存: {batches_file}")
        print(f"   包含 {len(batches)} 个下载批次")
        
        return sequence_file, batches_file
    
    def generate_complete_sequence(self):
        """生成完整的时间序列"""
        print(f"🪐 生成严格木星日时间序列")
        print("=" * 60)
        
        # 1. 生成时间序列
        time_sequence = self.generate_time_sequence()
        
        # 2. 验证时间序列
        is_valid = self.validate_time_sequence(time_sequence)
        
        if not is_valid:
            print("❌ 时间序列验证失败")
            return None, None
        
        # 3. 创建下载批次
        batches = self.create_download_batches(time_sequence)
        
        # 4. 保存数据
        sequence_file, batches_file = self.save_time_sequence(time_sequence, batches)
        
        print(f"\n🎉 严格木星日时间序列生成完成！")
        print(f"时间序列文件: {sequence_file}")
        print(f"下载批次文件: {batches_file}")
        print(f"总时间点数: {len(time_sequence):,}")
        print(f"下载批次数: {len(batches)}")
        
        return sequence_file, batches_file

def main():
    """主函数"""
    print("🪐 严格木星日时间序列生成器")
    print("=" * 60)
    print("目标：生成绝对精确的木星日间隔时间序列")
    print("特点：")
    print("1. 严格按照9.925小时间隔")
    print("2. 考虑闰年等时间因素")
    print("3. 确保时间连续性")
    print("4. 为精确下载做准备")
    
    generator = JupiterDayTimeSequence()
    sequence_file, batches_file = generator.generate_complete_sequence()
    
    if sequence_file and batches_file:
        print(f"\n✅ 准备工作完成！")
        print("下一步：使用生成的时间序列进行精确下载")
    else:
        print(f"\n❌ 时间序列生成失败")

if __name__ == "__main__":
    main()
