#!/usr/bin/env python3
"""
实用精确下载器 - 基于595/596分钟批次混合策略
"""

import os
import json
import pandas as pd
import numpy as np
import warnings
import ssl
import urllib3
import time
from datetime import datetime

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

def download_practical_batch(batch):
    """下载实用批次"""
    batch_id = batch['batch_id']
    jpl_step = batch['jpl_step']
    start_date = batch['jpl_start_date']
    end_date = batch['jpl_end_date']
    expected_points = batch['expected_points']
    
    print(f"批次 {batch_id}: {jpl_step} | {start_date} 到 {end_date} (期望 {expected_points} 点)")
    
    try:
        from astroquery.jplhorizons import Horizons
        
        jupiter = Horizons(
            id='599',
            location='@sun',
            epochs={
                'start': start_date,
                'stop': end_date,
                'step': jpl_step
            }
        )
        
        vectors = jupiter.vectors()
        df = vectors.to_pandas()
        
        if len(df) > 0:
            # 处理数据
            processed_df = process_data(df)
            
            # 保存批次文件
            batch_file = f"planet_data_practical/practical_batch_{batch_id:03d}.csv"
            os.makedirs('planet_data_practical', exist_ok=True)
            processed_df.to_csv(batch_file, index=False, encoding='utf-8-sig')
            
            print(f"   ✅ 成功: {len(processed_df)} 个点")
            
            return {
                'batch_id': batch_id,
                'status': 'success',
                'points': len(processed_df),
                'expected': expected_points,
                'step': jpl_step,
                'file': batch_file
            }
        else:
            print(f"   ⚠️ 无数据")
            return {
                'batch_id': batch_id,
                'status': 'no_data',
                'points': 0,
                'expected': expected_points,
                'step': jpl_step
            }
            
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return {
            'batch_id': batch_id,
            'status': 'failed',
            'points': 0,
            'expected': expected_points,
            'step': jpl_step,
            'error': str(e)
        }

def process_data(df):
    """处理数据"""
    au_to_km = 149597870.7
    day_to_sec = 86400
    light_speed_au_per_sec = 1.0 / 499.004783836
    
    processed_df = pd.DataFrame()
    
    processed_df['julian_date'] = df['datetime_jd']
    processed_df['date'] = df['datetime_str']
    processed_df['x_position_au'] = df['x']
    processed_df['y_position_au'] = df['y']
    processed_df['z_position_au'] = df['z']
    
    processed_df['x_velocity_km_s'] = df['vx'] * au_to_km / day_to_sec
    processed_df['y_velocity_km_s'] = df['vy'] * au_to_km / day_to_sec
    processed_df['z_velocity_km_s'] = df['vz'] * au_to_km / day_to_sec
    
    processed_df['distance_au'] = df['range']
    processed_df['distance_km'] = df['range'] * au_to_km
    processed_df['light_time_sec'] = df['range'] / light_speed_au_per_sec
    processed_df['range_rate_km_s'] = df['range_rate'] * au_to_km / day_to_sec
    
    processed_df['speed_km_s'] = np.sqrt(
        processed_df['x_velocity_km_s']**2 + 
        processed_df['y_velocity_km_s']**2 + 
        processed_df['z_velocity_km_s']**2
    )
    
    return processed_df

def main():
    """主函数"""
    print("🛠️ 实用精确木星数据下载器")
    print("=" * 60)
    
    # 加载下载计划
    with open('jupiter_practical_download_plan.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    batches = data['batches']
    print(f"加载了 {len(batches)} 个批次")
    
    # 执行下载
    results = []
    successful = 0
    total_points = 0
    
    for i, batch in enumerate(batches, 1):
        print(f"\n进度: {i}/{len(batches)}")
        
        result = download_practical_batch(batch)
        results.append(result)
        
        if result['status'] == 'success':
            successful += 1
            total_points += result['points']
        
        # 批次间延迟
        if i < len(batches):
            time.sleep(2)
    
    # 保存结果
    with open('practical_download_results.json', 'w', encoding='utf-8') as f:
        json.dump({
            'summary': {
                'total_batches': len(batches),
                'successful_batches': successful,
                'total_points': total_points,
                'download_time': datetime.now().isoformat()
            },
            'results': results
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 下载完成:")
    print(f"   成功批次: {successful}/{len(batches)}")
    print(f"   总数据点: {total_points:,}")

if __name__ == "__main__":
    main()
