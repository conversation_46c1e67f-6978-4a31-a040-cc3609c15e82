#!/usr/bin/env python3
"""
测试木星日步长格式
找到JPL HORIZONS接受的正确步长格式
"""

import os
import warnings
import ssl
import urllib3

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

def test_step_formats():
    """测试不同的步长格式"""
    print("🧪 测试木星日步长格式")
    print("=" * 50)
    
    # 木星日 = 9.9250小时 = 0.4135416667天
    step_formats = [
        ('9.925h', '小时格式（4位小数）'),
        ('9.93h', '小时格式（2位小数）'),
        ('10h', '整数小时'),
        ('0.414d', '天格式（3位小数）'),
        ('0.41d', '天格式（2位小数）'),
        ('595.5m', '分钟格式（9.925*60）'),
        ('596m', '整数分钟'),
    ]
    
    try:
        from astroquery.jplhorizons import Horizons
        
        for step, description in step_formats:
            print(f"\n测试步长: {step} ({description})")
            
            try:
                jupiter = Horizons(
                    id='599',
                    location='@sun',
                    epochs={
                        'start': '2024-12-29',
                        'stop': '2024-12-31',
                        'step': step
                    }
                )
                
                vectors = jupiter.vectors()
                print(f"✅ 成功！数据点: {len(vectors)}")
                
                # 如果成功，保存一个样本
                if len(vectors) > 0:
                    df = vectors.to_pandas()
                    sample_file = f'planet_data/木星_测试_{step.replace(".", "_")}.csv'
                    os.makedirs('planet_data', exist_ok=True)
                    df.to_csv(sample_file, index=False, encoding='utf-8-sig')
                    print(f"   样本保存: {sample_file}")
                    
                    # 显示时间间隔
                    if len(df) > 1:
                        time_diff = df['datetime_jd'].iloc[1] - df['datetime_jd'].iloc[0]
                        hours_diff = time_diff * 24
                        print(f"   实际间隔: {hours_diff:.4f} 小时")
                
            except Exception as e:
                print(f"❌ 失败: {e}")
                
    except ImportError:
        print("❌ astroquery未安装")

def test_best_format():
    """使用最佳格式测试一周数据"""
    print(f"\n🎯 使用最佳格式测试一周数据")
    print("=" * 50)
    
    # 使用最可能成功的格式
    best_step = '9.93h'  # 接近9.925的简化版本
    
    try:
        from astroquery.jplhorizons import Horizons
        
        print(f"步长: {best_step}")
        print("时间范围: 2024-12-25 到 2024-12-31")
        
        jupiter = Horizons(
            id='599',
            location='@sun',
            epochs={
                'start': '2024-12-25',
                'stop': '2024-12-31',
                'step': best_step
            }
        )
        
        print("正在查询JPL HORIZONS...")
        vectors = jupiter.vectors()
        
        print(f"✅ 成功获取 {len(vectors)} 个数据点")
        
        # 处理数据
        df = vectors.to_pandas()
        
        # 计算实际时间间隔
        if len(df) > 1:
            time_diffs = df['datetime_jd'].diff().dropna() * 24  # 转换为小时
            avg_interval = time_diffs.mean()
            print(f"平均时间间隔: {avg_interval:.4f} 小时")
            print(f"目标间隔: 9.925 小时")
            print(f"误差: {abs(avg_interval - 9.925):.4f} 小时")
        
        # 保存数据
        output_file = 'planet_data/木星_一周测试_最佳格式.csv'
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"数据已保存: {output_file}")
        
        # 显示数据预览
        print(f"\n📋 数据预览:")
        print(f"列: {list(df.columns)}")
        print(f"时间范围: {df['datetime_str'].iloc[0]} 到 {df['datetime_str'].iloc[-1]}")
        print(f"距离范围: {df['range'].min():.4f} - {df['range'].max():.4f} AU")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🪐 木星日步长格式测试")
    print("=" * 60)
    
    # 测试1：不同步长格式
    test_step_formats()
    
    # 测试2：最佳格式一周数据
    success = test_best_format()
    
    if success:
        print(f"\n🎉 测试完成！找到可用的步长格式。")
        print("可以继续下载完整的木星公转周期数据。")
    else:
        print(f"\n❌ 所有格式都失败了。")
        print("可能需要检查网络连接或使用不同的时间间隔。")

if __name__ == "__main__":
    main()
