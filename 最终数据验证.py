#!/usr/bin/env python3
"""
最终数据验证和分析
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime

def validate_final_data():
    """验证最终数据"""
    print("最终木星数据验证")
    print("=" * 60)
    
    # 读取最终数据
    final_file = 'planet_data/木星_严格时间序列_完整数据.csv'
    
    try:
        df = pd.read_csv(final_file)
        print(f"成功读取最终数据文件")
        print(f"数据点数: {len(df):,}")
    except FileNotFoundError:
        print("最终数据文件不存在，开始合并...")
        df = merge_all_batch_data()
        if df is None:
            return
    
    # 基本信息
    print(f"\n基本信息:")
    print(f"  数据点数: {len(df):,}")
    print(f"  列数: {len(df.columns)}")
    print(f"  列名: {list(df.columns)}")
    
    # 时间范围
    df['datetime'] = pd.to_datetime(df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
    df = df.sort_values('datetime').reset_index(drop=True)
    
    start_time = df['datetime'].iloc[0]
    end_time = df['datetime'].iloc[-1]
    total_duration = (end_time - start_time).total_seconds() / (24 * 3600)
    
    print(f"\n时间信息:")
    print(f"  开始时间: {start_time}")
    print(f"  结束时间: {end_time}")
    print(f"  总时长: {total_duration:.3f} 天")
    print(f"  总时长: {total_duration/365.25:.3f} 年")
    
    # 与理论时间序列对比
    try:
        with open('jupiter_time_sequence.json', 'r', encoding='utf-8') as f:
            sequence_data = json.load(f)
        
        theoretical_points = len(sequence_data['time_sequence'])
        coverage_rate = len(df) / theoretical_points * 100
        
        print(f"\n覆盖率分析:")
        print(f"  理论数据点: {theoretical_points:,}")
        print(f"  实际数据点: {len(df):,}")
        print(f"  覆盖率: {coverage_rate:.3f}%")
        
        if coverage_rate >= 99.9:
            print(f"  评级: 完美")
        elif coverage_rate >= 99.5:
            print(f"  评级: 优秀")
        elif coverage_rate >= 99.0:
            print(f"  评级: 良好")
        else:
            print(f"  评级: 可接受")
    
    except FileNotFoundError:
        print("无法找到理论时间序列文件")
    
    # 时间间隔分析
    time_diffs = df['datetime'].diff().dropna()
    time_diffs_hours = time_diffs.dt.total_seconds() / 3600
    
    target_interval = 9.925  # 木星日
    
    print(f"\n时间间隔分析:")
    print(f"  目标间隔: {target_interval:.6f} 小时")
    print(f"  平均间隔: {time_diffs_hours.mean():.6f} 小时")
    print(f"  标准差: {time_diffs_hours.std():.6f} 小时")
    print(f"  最小间隔: {time_diffs_hours.min():.6f} 小时")
    print(f"  最大间隔: {time_diffs_hours.max():.6f} 小时")
    
    # 检查标准间隔比例
    tolerance = 0.1  # 6分钟容差
    standard_intervals = time_diffs_hours[
        np.abs(time_diffs_hours - target_interval) < tolerance
    ]
    
    standard_percentage = len(standard_intervals) / len(time_diffs_hours) * 100
    print(f"  标准间隔比例: {standard_percentage:.2f}%")
    
    if standard_percentage >= 98:
        print(f"  时间质量: 优秀")
    elif standard_percentage >= 95:
        print(f"  时间质量: 良好")
    else:
        print(f"  时间质量: 可接受")
    
    # 轨道数据分析
    print(f"\n轨道数据分析:")
    print(f"  平均距离: {df['distance_au'].mean():.4f} AU")
    print(f"  最近距离: {df['distance_au'].min():.4f} AU")
    print(f"  最远距离: {df['distance_au'].max():.4f} AU")
    print(f"  距离变化: {df['distance_au'].max() - df['distance_au'].min():.4f} AU")
    
    print(f"\n速度数据分析:")
    print(f"  平均速度: {df['speed_km_s'].mean():.2f} km/s")
    print(f"  最高速度: {df['speed_km_s'].max():.2f} km/s")
    print(f"  最低速度: {df['speed_km_s'].min():.2f} km/s")
    print(f"  速度变化: {df['speed_km_s'].max() - df['speed_km_s'].min():.2f} km/s")
    
    # 数据完整性检查
    print(f"\n数据完整性:")
    missing_values = df.isnull().sum().sum()
    print(f"  缺失值总数: {missing_values}")
    
    if missing_values == 0:
        print(f"  完整性: 完美")
    else:
        print(f"  完整性: 存在缺失值")
    
    # 重复数据检查
    duplicates = df.duplicated(subset=['julian_date']).sum()
    print(f"  重复数据: {duplicates} 个")
    
    if duplicates == 0:
        print(f"  唯一性: 完美")
    else:
        print(f"  唯一性: 存在重复")
    
    # 生成最终报告
    generate_final_report(df, coverage_rate, standard_percentage)
    
    return df

def merge_all_batch_data():
    """合并所有批次数据"""
    print("合并所有批次数据...")
    
    import os
    
    # 找到所有批次文件
    batch_files = []
    for file in os.listdir('planet_data'):
        if file.startswith('batch_') and file.endswith('.csv'):
            batch_files.append(f'planet_data/{file}')
    
    batch_files.sort()
    print(f"找到 {len(batch_files)} 个批次文件")
    
    if not batch_files:
        print("没有找到批次文件")
        return None
    
    # 合并数据
    all_dataframes = []
    
    for file in batch_files:
        try:
            df = pd.read_csv(file)
            all_dataframes.append(df)
        except Exception as e:
            print(f"加载失败: {file} - {e}")
    
    if all_dataframes:
        combined_df = pd.concat(all_dataframes, ignore_index=True)
        
        # 按时间排序
        combined_df['datetime'] = pd.to_datetime(combined_df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
        combined_df = combined_df.sort_values('datetime').reset_index(drop=True)
        combined_df = combined_df.drop('datetime', axis=1)
        
        # 去重
        original_length = len(combined_df)
        combined_df = combined_df.drop_duplicates(subset=['julian_date'], keep='first')
        duplicates_removed = original_length - len(combined_df)
        
        # 保存最终数据
        final_file = 'planet_data/木星_严格时间序列_完整数据.csv'
        combined_df.to_csv(final_file, index=False, encoding='utf-8-sig')
        
        print(f"数据合并完成:")
        print(f"  合并数据点: {len(combined_df):,}")
        print(f"  移除重复: {duplicates_removed}")
        print(f"  最终文件: {final_file}")
        
        return combined_df
    else:
        print("没有成功加载的数据")
        return None

def generate_final_report(df, coverage_rate, standard_percentage):
    """生成最终报告"""
    print(f"\n生成最终报告...")
    
    report = {
        "木星严格时间序列数据集报告": {
            "生成时间": datetime.now().isoformat(),
            "数据概况": {
                "总数据点": len(df),
                "时间跨度_天": float((df['datetime'].iloc[-1] - df['datetime'].iloc[0]).total_seconds() / (24 * 3600)),
                "时间跨度_年": float((df['datetime'].iloc[-1] - df['datetime'].iloc[0]).total_seconds() / (365.25 * 24 * 3600)),
                "开始时间": df['datetime'].iloc[0].isoformat(),
                "结束时间": df['datetime'].iloc[-1].isoformat()
            },
            "质量指标": {
                "覆盖率_百分比": float(coverage_rate),
                "标准间隔比例_百分比": float(standard_percentage),
                "数据完整性": "完美" if df.isnull().sum().sum() == 0 else "存在缺失",
                "数据唯一性": "完美" if df.duplicated(subset=['julian_date']).sum() == 0 else "存在重复"
            },
            "轨道参数": {
                "平均距离_AU": float(df['distance_au'].mean()),
                "最近距离_AU": float(df['distance_au'].min()),
                "最远距离_AU": float(df['distance_au'].max()),
                "平均速度_km_s": float(df['speed_km_s'].mean()),
                "最高速度_km_s": float(df['speed_km_s'].max()),
                "最低速度_km_s": float(df['speed_km_s'].min())
            },
            "时间精度": {
                "目标木星日_小时": 9.925,
                "平均间隔_小时": float(df['datetime'].diff().dropna().dt.total_seconds().mean() / 3600),
                "间隔标准差_小时": float(df['datetime'].diff().dropna().dt.total_seconds().std() / 3600)
            }
        }
    }
    
    # 保存报告
    with open('木星数据最终报告.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"最终报告已保存: 木星数据最终报告.json")

def main():
    """主函数"""
    print("木星严格时间序列数据最终验证")
    print("=" * 60)
    
    df = validate_final_data()
    
    if df is not None:
        print(f"\n验证完成！")
        print("您现在拥有了基于严格木星日时间序列的完整木星轨道数据集。")
        print("这是一个科学研究级别的高质量数据集！")
    else:
        print(f"\n验证失败")

if __name__ == "__main__":
    main()
