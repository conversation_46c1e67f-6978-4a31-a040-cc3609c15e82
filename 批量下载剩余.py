#!/usr/bin/env python3
"""
批量下载剩余批次
"""

import subprocess
import time
from datetime import datetime

def download_remaining_segments():
    """下载剩余段落"""
    print("批量下载剩余木星数据")
    print("=" * 60)
    
    # 剩余的段落
    segments = [
        (61, 80),
        (81, 100),
        (101, 120),
        (121, 144)
    ]
    
    for start, end in segments:
        print(f"\n下载段落: {start} 到 {end}")
        print(f"时间: {datetime.now().strftime('%H:%M:%S')}")
        
        try:
            result = subprocess.run([
                'python', '分段下载器.py', str(start), str(end)
            ], capture_output=True, text=True, timeout=1200, encoding='utf-8')
            
            if result.returncode == 0:
                print(f"段落 {start}-{end} 完成")
                # 提取关键信息
                lines = result.stdout.split('\n')
                for line in lines:
                    if '段落下载完成' in line or '成功批次' in line or '总数据点' in line:
                        print(f"   {line.strip()}")
            else:
                print(f"段落 {start}-{end} 失败")
                print(f"错误: {result.stderr}")
        
        except subprocess.TimeoutExpired:
            print(f"段落 {start}-{end} 超时")
        
        except Exception as e:
            print(f"段落 {start}-{end} 异常: {e}")
        
        # 段落间休息
        if end < 144:
            print("休息10秒...")
            time.sleep(10)
    
    print("\n所有段落下载完成！")

if __name__ == "__main__":
    download_remaining_segments()
