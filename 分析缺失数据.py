#!/usr/bin/env python3
"""
分析严格时间序列中的缺失数据
找出具体缺失的时间点并尝试补充
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def analyze_missing_data():
    """分析缺失的数据"""
    print("🔍 分析严格时间序列中的缺失数据")
    print("=" * 60)
    
    # 1. 加载理论时间序列
    with open('jupiter_time_sequence.json', 'r', encoding='utf-8') as f:
        sequence_data = json.load(f)
    
    theoretical_sequence = sequence_data['time_sequence']
    print(f"理论时间序列: {len(theoretical_sequence):,} 个时间点")
    
    # 2. 加载实际数据
    df = pd.read_csv('planet_data/木星_严格时间序列_完整数据.csv')
    print(f"实际数据: {len(df):,} 个数据点")
    
    # 3. 转换为可比较的格式
    theoretical_times = set()
    for point in theoretical_sequence:
        # 转换为儒略日进行比较
        theoretical_times.add(point['julian_date'])
    
    actual_times = set(df['julian_date'].values)
    
    print(f"理论时间点: {len(theoretical_times):,}")
    print(f"实际时间点: {len(actual_times):,}")
    
    # 4. 找出缺失的时间点
    missing_times = theoretical_times - actual_times
    extra_times = actual_times - theoretical_times
    
    print(f"\n📊 对比结果:")
    print(f"缺失时间点: {len(missing_times):,}")
    print(f"额外时间点: {len(extra_times):,}")
    print(f"覆盖率: {len(actual_times)/len(theoretical_times)*100:.3f}%")
    
    # 5. 分析缺失时间点的分布
    if missing_times:
        print(f"\n🕳️ 缺失时间点分析:")
        
        # 将缺失的儒略日转换回时间点信息
        missing_points = []
        for point in theoretical_sequence:
            if point['julian_date'] in missing_times:
                missing_points.append(point)
        
        # 按时间排序
        missing_points.sort(key=lambda x: x['julian_date'])
        
        print(f"缺失点总数: {len(missing_points)}")
        
        # 分析缺失点的时间分布
        missing_by_year = {}
        for point in missing_points:
            year = point['year']
            missing_by_year[year] = missing_by_year.get(year, 0) + 1
        
        print(f"\n按年份分布:")
        for year in sorted(missing_by_year.keys()):
            print(f"  {year}年: {missing_by_year[year]} 个缺失点")
        
        # 显示前20个缺失点
        print(f"\n前20个缺失时间点:")
        for i, point in enumerate(missing_points[:20]):
            dt_str = point['datetime'][:19]
            print(f"  {i+1:2d}. {dt_str} (儒略日: {point['julian_date']:.6f})")
        
        if len(missing_points) > 20:
            print(f"  ... 还有 {len(missing_points) - 20} 个缺失点")
        
        return missing_points
    else:
        print("✅ 没有缺失的时间点！")
        return []

def find_missing_time_ranges(missing_points):
    """找出缺失时间点的连续范围"""
    if not missing_points:
        return []
    
    print(f"\n📅 分析缺失时间范围:")
    
    # 按时间排序
    missing_points.sort(key=lambda x: x['julian_date'])
    
    # 找出连续的缺失范围
    ranges = []
    current_range_start = missing_points[0]
    current_range_end = missing_points[0]
    
    jupiter_day_hours = 9.925
    jupiter_day_jd = jupiter_day_hours / 24  # 转换为儒略日
    
    for i in range(1, len(missing_points)):
        prev_point = missing_points[i-1]
        curr_point = missing_points[i]
        
        # 检查是否连续（允许小的误差）
        expected_jd = prev_point['julian_date'] + jupiter_day_jd
        actual_jd = curr_point['julian_date']
        
        if abs(actual_jd - expected_jd) < 0.01:  # 连续
            current_range_end = curr_point
        else:  # 不连续，开始新范围
            ranges.append({
                'start': current_range_start,
                'end': current_range_end,
                'count': 1 + int((current_range_end['julian_date'] - current_range_start['julian_date']) / jupiter_day_jd)
            })
            current_range_start = curr_point
            current_range_end = curr_point
    
    # 添加最后一个范围
    ranges.append({
        'start': current_range_start,
        'end': current_range_end,
        'count': 1 + int((current_range_end['julian_date'] - current_range_start['julian_date']) / jupiter_day_jd)
    })
    
    print(f"发现 {len(ranges)} 个缺失时间范围:")
    
    for i, range_info in enumerate(ranges, 1):
        start_time = range_info['start']['datetime'][:19]
        end_time = range_info['end']['datetime'][:19]
        count = range_info['count']
        
        print(f"  范围 {i}: {start_time} 到 {end_time} ({count} 个点)")
    
    return ranges

def create_supplementary_download_plan(missing_ranges):
    """创建补充下载计划"""
    if not missing_ranges:
        print("✅ 无需补充下载")
        return []
    
    print(f"\n📋 创建补充下载计划:")
    
    download_tasks = []
    
    for i, range_info in enumerate(missing_ranges, 1):
        start_point = range_info['start']
        end_point = range_info['end']
        
        # 转换为datetime对象
        start_dt = datetime.fromisoformat(start_point['datetime'])
        end_dt = datetime.fromisoformat(end_point['datetime'])
        
        # 扩展时间范围以确保覆盖（前后各加1天）
        extended_start = start_dt - timedelta(days=1)
        extended_end = end_dt + timedelta(days=1)
        
        task = {
            'task_id': i,
            'description': f"补充缺失范围 {i}",
            'original_start': start_dt.isoformat(),
            'original_end': end_dt.isoformat(),
            'download_start': extended_start.strftime('%Y-%m-%d'),
            'download_end': extended_end.strftime('%Y-%m-%d'),
            'expected_points': range_info['count'],
            'missing_points': range_info['count']
        }
        
        download_tasks.append(task)
        
        print(f"  任务 {i}: {task['download_start']} 到 {task['download_end']}")
        print(f"    原始范围: {start_dt.strftime('%Y-%m-%d %H:%M')} 到 {end_dt.strftime('%Y-%m-%d %H:%M')}")
        print(f"    缺失点数: {range_info['count']}")
    
    # 保存补充下载计划
    with open('supplementary_download_plan.json', 'w', encoding='utf-8') as f:
        json.dump({
            'metadata': {
                'total_tasks': len(download_tasks),
                'total_missing_points': sum(task['missing_points'] for task in download_tasks),
                'creation_time': datetime.now().isoformat()
            },
            'tasks': download_tasks
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 补充下载计划已保存: supplementary_download_plan.json")
    print(f"总任务数: {len(download_tasks)}")
    print(f"总缺失点数: {sum(task['missing_points'] for task in download_tasks)}")
    
    return download_tasks

def analyze_why_missing():
    """分析为什么会有缺失数据"""
    print(f"\n🤔 分析缺失数据的原因:")
    
    # 检查下载结果文件
    import os
    result_files = [f for f in os.listdir('.') if f.startswith('download_results_') and f.endswith('.json')]
    
    total_downloaded = 0
    total_expected = 0
    failed_batches = []
    
    for file in result_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for result in data['results']:
                total_expected += result['expected']
                if result['status'] == 'success':
                    total_downloaded += result['points']
                else:
                    failed_batches.append(result)
        except:
            continue
    
    print(f"下载统计:")
    print(f"  总期望点数: {total_expected:,}")
    print(f"  总下载点数: {total_downloaded:,}")
    print(f"  下载效率: {total_downloaded/total_expected*100:.2f}%")
    print(f"  失败批次: {len(failed_batches)}")
    
    if failed_batches:
        print(f"\n失败批次详情:")
        for batch in failed_batches[:5]:  # 只显示前5个
            print(f"  批次 {batch['batch_id']}: {batch.get('error', 'Unknown error')}")
    
    # 可能的原因分析
    print(f"\n可能的缺失原因:")
    print(f"  1. JPL HORIZONS 596分钟格式限制")
    print(f"     - 596分钟 ≠ 595.5分钟（精确木星日）")
    print(f"     - 累积误差导致时间点偏移")
    
    print(f"  2. 批次边界处理")
    print(f"     - 30天批次可能不完全对齐木星日")
    print(f"     - JPL系统的时间对齐策略")
    
    print(f"  3. 数据去重过程")
    print(f"     - 相近时间点被误认为重复")
    print(f"     - 儒略日精度限制")

def main():
    """主函数"""
    print("🪐 严格时间序列缺失数据分析")
    print("=" * 60)
    
    # 1. 分析缺失数据
    missing_points = analyze_missing_data()
    
    # 2. 找出缺失范围
    missing_ranges = find_missing_time_ranges(missing_points)
    
    # 3. 创建补充下载计划
    download_tasks = create_supplementary_download_plan(missing_ranges)
    
    # 4. 分析缺失原因
    analyze_why_missing()
    
    # 5. 总结
    print(f"\n📋 总结:")
    if missing_points:
        print(f"  缺失数据点: {len(missing_points):,}")
        print(f"  缺失范围: {len(missing_ranges)}")
        print(f"  补充任务: {len(download_tasks)}")
        print(f"  建议: 执行补充下载以达到100%覆盖率")
    else:
        print(f"  ✅ 数据完整，无需补充")

if __name__ == "__main__":
    main()
