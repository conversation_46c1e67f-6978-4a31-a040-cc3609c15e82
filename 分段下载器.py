#!/usr/bin/env python3
"""
分段下载器 - 每次下载20个批次
"""

import os
import json
import pandas as pd
import numpy as np
import warnings
import ssl
import urllib3
import time
from datetime import datetime

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

def download_batch_segment(start_batch, end_batch):
    """下载指定范围的批次"""
    print(f"下载批次 {start_batch} 到 {end_batch}")
    print("=" * 60)
    
    # 加载批次信息
    with open('jupiter_download_batches.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    batches = data['batches']
    
    # 选择要下载的批次
    target_batches = [b for b in batches if start_batch <= b['batch_id'] <= end_batch]
    
    print(f"目标批次数: {len(target_batches)}")
    
    results = []
    successful = 0
    total_points = 0
    
    for i, batch in enumerate(target_batches, 1):
        batch_id = batch['batch_id']
        start_date = batch['jpl_start_date']
        end_date = batch['jpl_end_date']
        expected_points = batch['time_points']
        
        print(f"\n批次 {batch_id} ({i}/{len(target_batches)})")
        print(f"   时间: {start_date} 到 {end_date}")
        print(f"   期望: {expected_points} 个点")
        
        try:
            from astroquery.jplhorizons import Horizons
            
            jupiter = Horizons(
                id='599',
                location='@sun',
                epochs={
                    'start': start_date,
                    'stop': end_date,
                    'step': '596m'
                }
            )
            
            vectors = jupiter.vectors()
            df = vectors.to_pandas()
            
            if len(df) > 0:
                # 处理数据
                processed_df = process_data(df)
                
                # 保存批次文件
                batch_file = f"planet_data/batch_{batch_id:03d}.csv"
                os.makedirs('planet_data', exist_ok=True)
                processed_df.to_csv(batch_file, index=False, encoding='utf-8-sig')
                
                print(f"   成功: {len(processed_df)} 个点")
                
                results.append({
                    'batch_id': batch_id,
                    'status': 'success',
                    'points': len(processed_df),
                    'expected': expected_points,
                    'file': batch_file
                })
                
                successful += 1
                total_points += len(processed_df)
                
            else:
                print(f"   无数据")
                results.append({
                    'batch_id': batch_id,
                    'status': 'no_data',
                    'points': 0,
                    'expected': expected_points
                })
                
        except Exception as e:
            print(f"   失败: {e}")
            results.append({
                'batch_id': batch_id,
                'status': 'failed',
                'points': 0,
                'expected': expected_points,
                'error': str(e)
            })
        
        # 批次间延迟
        if i < len(target_batches):
            time.sleep(2)
    
    # 保存结果
    result_file = f'download_results_{start_batch}_{end_batch}.json'
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump({
            'segment': f'{start_batch}-{end_batch}',
            'total_batches': len(target_batches),
            'successful_batches': successful,
            'total_points': total_points,
            'download_time': datetime.now().isoformat(),
            'results': results
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n段落下载完成:")
    print(f"   成功批次: {successful}/{len(target_batches)}")
    print(f"   总数据点: {total_points:,}")
    print(f"   结果文件: {result_file}")
    
    return results

def process_data(df):
    """处理原始数据"""
    au_to_km = 149597870.7
    day_to_sec = 86400
    light_speed_au_per_sec = 1.0 / 499.004783836
    
    processed_df = pd.DataFrame()
    
    processed_df['julian_date'] = df['datetime_jd']
    processed_df['date'] = df['datetime_str']
    processed_df['x_position_au'] = df['x']
    processed_df['y_position_au'] = df['y']
    processed_df['z_position_au'] = df['z']
    
    processed_df['x_velocity_km_s'] = df['vx'] * au_to_km / day_to_sec
    processed_df['y_velocity_km_s'] = df['vy'] * au_to_km / day_to_sec
    processed_df['z_velocity_km_s'] = df['vz'] * au_to_km / day_to_sec
    
    processed_df['distance_au'] = df['range']
    processed_df['distance_km'] = df['range'] * au_to_km
    processed_df['light_time_sec'] = df['range'] / light_speed_au_per_sec
    processed_df['range_rate_km_s'] = df['range_rate'] * au_to_km / day_to_sec
    
    processed_df['speed_km_s'] = np.sqrt(
        processed_df['x_velocity_km_s']**2 + 
        processed_df['y_velocity_km_s']**2 + 
        processed_df['z_velocity_km_s']**2
    )
    
    return processed_df

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) != 3:
        print("用法: python 分段下载器.py <起始批次> <结束批次>")
        print("例如: python 分段下载器.py 1 20")
        return
    
    try:
        start_batch = int(sys.argv[1])
        end_batch = int(sys.argv[2])
    except ValueError:
        print("批次号必须是整数")
        return
    
    print(f"木星数据分段下载器")
    print(f"下载批次: {start_batch} 到 {end_batch}")
    
    download_batch_segment(start_batch, end_batch)

if __name__ == "__main__":
    main()
