#!/usr/bin/env python3
"""
专门下载缺失的两天数据：2016年8月15日、2017年3月22日
尝试不同的时间范围和策略
"""

import os
import pandas as pd
import numpy as np
import warnings
import ssl
import urllib3
import time
from datetime import datetime, timedelta

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

class MissingDataRetriever:
    def __init__(self):
        """初始化缺失数据下载器"""
        self.step_size = '596m'  # 596分钟
        self.missing_dates = [
            {
                'date': '2016-08-15',
                'description': '2016年8月15日',
                'strategies': [
                    {'start': '2016-08-14', 'end': '2016-08-16'},  # 3天范围
                    {'start': '2016-08-13', 'end': '2016-08-17'},  # 5天范围
                    {'start': '2016-08-10', 'end': '2016-08-20'},  # 10天范围
                ]
            },
            {
                'date': '2017-03-22',
                'description': '2017年3月22日',
                'strategies': [
                    {'start': '2017-03-21', 'end': '2017-03-23'},  # 3天范围
                    {'start': '2017-03-20', 'end': '2017-03-24'},  # 5天范围
                    {'start': '2017-03-17', 'end': '2017-03-27'},  # 10天范围
                ]
            }
        ]
        
    def download_with_strategy(self, strategy, description):
        """使用特定策略下载数据"""
        print(f"  策略: {strategy['start']} 到 {strategy['end']}")
        
        try:
            from astroquery.jplhorizons import Horizons
            
            jupiter = Horizons(
                id='599',  # 木星
                location='@sun',
                epochs={
                    'start': strategy['start'],
                    'stop': strategy['end'],
                    'step': self.step_size
                }
            )
            
            vectors = jupiter.vectors()
            df = vectors.to_pandas()
            
            if len(df) > 0:
                processed_df = self.process_data(df)
                print(f"  ✅ 成功获取 {len(processed_df)} 个数据点")
                return processed_df
            else:
                print(f"  ⚠️ 无数据返回")
                return None
                
        except Exception as e:
            print(f"  ❌ 失败: {e}")
            return None
    
    def process_data(self, df):
        """处理原始数据"""
        au_to_km = 149597870.7
        day_to_sec = 86400
        light_speed_au_per_sec = 1.0 / 499.004783836
        
        processed_df = pd.DataFrame()
        
        processed_df['julian_date'] = df['datetime_jd']
        processed_df['date'] = df['datetime_str']
        processed_df['x_position_au'] = df['x']
        processed_df['y_position_au'] = df['y']
        processed_df['z_position_au'] = df['z']
        
        processed_df['x_velocity_km_s'] = df['vx'] * au_to_km / day_to_sec
        processed_df['y_velocity_km_s'] = df['vy'] * au_to_km / day_to_sec
        processed_df['z_velocity_km_s'] = df['vz'] * au_to_km / day_to_sec
        
        processed_df['distance_au'] = df['range']
        processed_df['distance_km'] = df['range'] * au_to_km
        processed_df['light_time_sec'] = df['range'] / light_speed_au_per_sec
        processed_df['range_rate_km_s'] = df['range_rate'] * au_to_km / day_to_sec
        
        processed_df['speed_km_s'] = np.sqrt(
            processed_df['x_velocity_km_s']**2 + 
            processed_df['y_velocity_km_s']**2 + 
            processed_df['z_velocity_km_s']**2
        )
        
        return processed_df
    
    def try_alternative_steps(self, date_info):
        """尝试不同的时间步长"""
        print(f"  尝试不同时间步长...")
        
        alternative_steps = [
            ('10h', '10小时间隔'),
            ('8h', '8小时间隔'),
            ('12h', '12小时间隔'),
            ('1d', '1天间隔'),
            ('0.5d', '12小时间隔（天格式）')
        ]
        
        for step, desc in alternative_steps:
            try:
                print(f"    测试 {desc}")
                
                from astroquery.jplhorizons import Horizons
                
                jupiter = Horizons(
                    id='599',
                    location='@sun',
                    epochs={
                        'start': date_info['strategies'][0]['start'],
                        'stop': date_info['strategies'][0]['end'],
                        'step': step
                    }
                )
                
                vectors = jupiter.vectors()
                df = vectors.to_pandas()
                
                if len(df) > 0:
                    processed_df = self.process_data(df)
                    print(f"    ✅ {desc} 成功: {len(processed_df)} 个点")
                    return processed_df
                else:
                    print(f"    ⚠️ {desc} 无数据")
                    
            except Exception as e:
                print(f"    ❌ {desc} 失败: {e}")
        
        return None
    
    def download_missing_data(self):
        """下载缺失的数据"""
        print("🔍 尝试下载缺失的两天数据")
        print("=" * 60)
        
        all_recovered_data = []
        success_count = 0
        
        for date_info in self.missing_dates:
            print(f"\n📅 处理 {date_info['description']}")
            print("-" * 40)
            
            recovered_data = None
            
            # 策略1：尝试不同时间范围
            for i, strategy in enumerate(date_info['strategies'], 1):
                print(f"尝试策略 {i}:")
                recovered_data = self.download_with_strategy(strategy, date_info['description'])
                
                if recovered_data is not None:
                    break
                
                time.sleep(2)  # 策略间延迟
            
            # 策略2：如果常规方法失败，尝试不同步长
            if recovered_data is None:
                print(f"常规策略失败，尝试替代方案...")
                recovered_data = self.try_alternative_steps(date_info)
            
            # 保存结果
            if recovered_data is not None:
                # 保存单独文件
                filename = f"planet_data/木星_恢复数据_{date_info['date']}.csv"
                os.makedirs('planet_data', exist_ok=True)
                recovered_data.to_csv(filename, index=False, encoding='utf-8-sig')
                
                all_recovered_data.append(recovered_data)
                success_count += 1
                
                print(f"✅ {date_info['description']} 数据恢复成功")
                print(f"   文件: {filename}")
                print(f"   数据点: {len(recovered_data)}")
                
                # 显示数据预览
                target_date = date_info['date']
                date_data = recovered_data[recovered_data['date'].str.contains(target_date.replace('-', '-'))]
                if len(date_data) > 0:
                    print(f"   目标日期数据: {len(date_data)} 个点")
                    print(f"   时间范围: {date_data['date'].iloc[0]} 到 {date_data['date'].iloc[-1]}")
                
            else:
                print(f"❌ {date_info['description']} 数据恢复失败")
                print(f"   可能原因: JPL系统在该时间段确实无数据")
        
        # 总结
        print(f"\n📊 恢复结果总结")
        print("=" * 60)
        print(f"目标日期: 2个")
        print(f"成功恢复: {success_count}个")
        print(f"失败数量: {2 - success_count}个")
        
        if success_count > 0:
            total_recovered_points = sum(len(df) for df in all_recovered_data)
            print(f"总恢复数据点: {total_recovered_points}")
            
            # 合并恢复的数据
            if len(all_recovered_data) > 1:
                combined_recovered = pd.concat(all_recovered_data, ignore_index=True)
            else:
                combined_recovered = all_recovered_data[0]
            
            # 保存合并文件
            combined_file = 'planet_data/木星_恢复数据_合并.csv'
            combined_recovered.to_csv(combined_file, index=False, encoding='utf-8-sig')
            print(f"合并文件: {combined_file}")
            
            return combined_recovered
        else:
            print("❌ 未能恢复任何数据")
            return None
    
    def merge_with_existing_data(self, recovered_data):
        """将恢复的数据与现有数据合并"""
        if recovered_data is None:
            return False
        
        print(f"\n🔄 合并恢复数据与现有数据")
        print("=" * 60)
        
        try:
            # 读取现有数据
            existing_file = 'planet_data/木星_完整数据_含2025年1月.csv'
            existing_df = pd.read_csv(existing_file)
            
            print(f"现有数据: {len(existing_df):,} 个点")
            print(f"恢复数据: {len(recovered_data):,} 个点")
            
            # 合并数据
            combined_df = pd.concat([existing_df, recovered_data], ignore_index=True)
            
            # 按时间排序
            combined_df['datetime'] = pd.to_datetime(combined_df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
            combined_df = combined_df.sort_values('datetime').reset_index(drop=True)
            combined_df = combined_df.drop('datetime', axis=1)
            
            # 去重
            original_length = len(combined_df)
            combined_df = combined_df.drop_duplicates(subset=['julian_date'], keep='first')
            duplicates_removed = original_length - len(combined_df)
            
            print(f"合并后: {len(combined_df):,} 个点")
            if duplicates_removed > 0:
                print(f"移除重复: {duplicates_removed} 个点")
            
            # 保存最终数据
            final_file = 'planet_data/木星_最终完整数据_含恢复.csv'
            combined_df.to_csv(final_file, index=False, encoding='utf-8-sig')
            
            print(f"✅ 最终数据已保存: {final_file}")
            print(f"数据增加: {len(combined_df) - len(existing_df)} 个点")
            
            # 重新计算覆盖率
            self.recalculate_coverage(combined_df)
            
            return True
            
        except Exception as e:
            print(f"❌ 合并失败: {e}")
            return False
    
    def recalculate_coverage(self, df):
        """重新计算覆盖率"""
        print(f"\n📈 重新计算覆盖率")
        print("-" * 30)
        
        df['datetime'] = pd.to_datetime(df['date'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
        
        start_time = df['datetime'].iloc[0]
        end_time = df['datetime'].iloc[-1]
        total_duration = (end_time - start_time).total_seconds() / (24 * 3600)
        
        target_interval_hours = 596 / 60
        theoretical_points = total_duration * 24 / target_interval_hours
        coverage_rate = len(df) / theoretical_points * 100
        
        print(f"时间跨度: {total_duration:.1f} 天")
        print(f"理论数据点: {theoretical_points:.0f}")
        print(f"实际数据点: {len(df):,}")
        print(f"最终覆盖率: {coverage_rate:.3f}%")
        
        if coverage_rate > 100.7:
            print("🌟 覆盖率超过100.7%！数据非常完整！")
        elif coverage_rate > 100.0:
            print("✅ 覆盖率超过100%！数据完整！")
        else:
            print("📈 覆盖率已提升！")

def main():
    """主函数"""
    print("🪐 木星缺失数据恢复尝试")
    print("=" * 60)
    print("目标：恢复2016年8月15日、2017年3月22日的数据")
    
    retriever = MissingDataRetriever()
    
    # 尝试下载缺失数据
    recovered_data = retriever.download_missing_data()
    
    # 如果有恢复的数据，合并到现有数据中
    if recovered_data is not None:
        success = retriever.merge_with_existing_data(recovered_data)
        
        if success:
            print(f"\n🎉 缺失数据恢复完成！")
            print("现在您拥有了更完整的木星轨道数据。")
        else:
            print(f"\n⚠️ 数据恢复成功，但合并失败")
    else:
        print(f"\n📝 结论：这两天的数据可能确实在JPL系统中不存在")
        print("可能原因：")
        print("1. JPL HORIZONS系统在这些日期进行维护")
        print("2. 数据采集设备故障")
        print("3. 数据质量不符合发布标准")
        print("\n建议：使用插值法估算这两天的数据")

if __name__ == "__main__":
    main()
