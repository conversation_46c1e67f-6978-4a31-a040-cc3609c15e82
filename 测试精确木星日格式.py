#!/usr/bin/env python3
"""
测试JPL HORIZONS是否支持更精确的木星日格式
"""

import os
import warnings
import ssl
import urllib3

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

def test_precise_jupiter_day_formats():
    """测试不同的精确木星日格式"""
    print("🧪 测试JPL HORIZONS精确木星日格式支持")
    print("=" * 60)
    
    # 木星日的精确值
    jupiter_day_hours = 9.925  # 9小时55分30秒
    jupiter_day_days = jupiter_day_hours / 24  # 转换为天
    jupiter_day_seconds = jupiter_day_hours * 3600  # 转换为秒
    
    print(f"📊 木星日精确值:")
    print(f"  小时: {jupiter_day_hours}")
    print(f"  天: {jupiter_day_days:.10f}")
    print(f"  秒: {jupiter_day_seconds:.0f}")
    
    # 测试不同格式
    test_formats = [
        # 小数天格式（不同精度）
        ('0.4135d', '4位小数天'),
        ('0.41354d', '5位小数天'),
        ('0.413542d', '6位小数天'),
        ('0.4135417d', '7位小数天'),
        ('0.41354167d', '8位小数天'),
        
        # 小时格式
        ('9.925h', '小数小时'),
        
        # 秒格式
        ('35730s', '精确秒数'),
        
        # 分钟格式
        ('595.5m', '小数分钟'),
        
        # 混合格式
        ('595m30s', '分钟+秒'),
    ]
    
    print(f"\n🔬 格式支持测试:")
    
    for step_format, description in test_formats:
        print(f"\n测试格式: {step_format} ({description})")
        
        try:
            from astroquery.jplhorizons import Horizons
            
            # 测试小范围查询
            jupiter = Horizons(
                id='599',  # 木星
                location='@sun',
                epochs={
                    'start': '2024-01-01',
                    'stop': '2024-01-03',  # 只测试2天
                    'step': step_format
                }
            )
            
            vectors = jupiter.vectors()
            
            if len(vectors) > 0:
                print(f"  ✅ 支持！获得 {len(vectors)} 个数据点")
                
                # 分析时间间隔
                import pandas as pd
                df = vectors.to_pandas()
                df['datetime'] = pd.to_datetime(df['datetime_str'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
                
                if len(df) > 1:
                    time_diffs = df['datetime'].diff().dropna()
                    avg_interval_hours = time_diffs.dt.total_seconds().mean() / 3600
                    print(f"  平均间隔: {avg_interval_hours:.6f} 小时")
                    print(f"  与目标差异: {abs(avg_interval_hours - jupiter_day_hours):.6f} 小时")
                    
                    if abs(avg_interval_hours - jupiter_day_hours) < 0.001:
                        print(f"  🌟 精度优秀！")
                    elif abs(avg_interval_hours - jupiter_day_hours) < 0.01:
                        print(f"  ✅ 精度良好！")
                    else:
                        print(f"  ⚠️ 精度一般")
            else:
                print(f"  ⚠️ 支持但无数据返回")
                
        except Exception as e:
            error_msg = str(e)
            if "Bad dates" in error_msg:
                print(f"  ❌ 不支持：日期格式错误")
            elif "step" in error_msg.lower():
                print(f"  ❌ 不支持：步长格式错误")
            elif "format" in error_msg.lower():
                print(f"  ❌ 不支持：格式不识别")
            else:
                print(f"  ❌ 不支持：{error_msg[:50]}...")

def test_optimal_format():
    """测试最优格式"""
    print(f"\n🎯 测试最优格式")
    print("=" * 60)
    
    # 基于前面的测试结果，选择最有希望的格式
    optimal_candidates = [
        '0.41354167d',  # 最精确的天格式
        '0.413542d',    # 6位精度天格式
        '596m',         # 当前使用的格式（对照）
    ]
    
    for step_format in optimal_candidates:
        print(f"\n详细测试: {step_format}")
        
        try:
            from astroquery.jplhorizons import Horizons
            
            jupiter = Horizons(
                id='599',
                location='@sun',
                epochs={
                    'start': '2024-01-01',
                    'stop': '2024-01-08',  # 测试一周
                    'step': step_format
                }
            )
            
            vectors = jupiter.vectors()
            
            if len(vectors) > 0:
                import pandas as pd
                df = vectors.to_pandas()
                df['datetime'] = pd.to_datetime(df['datetime_str'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
                df = df.sort_values('datetime')
                
                print(f"  数据点数: {len(df)}")
                print(f"  时间范围: {df['datetime'].iloc[0]} 到 {df['datetime'].iloc[-1]}")
                
                if len(df) > 1:
                    time_diffs = df['datetime'].diff().dropna()
                    time_diffs_hours = time_diffs.dt.total_seconds() / 3600
                    
                    print(f"  平均间隔: {time_diffs_hours.mean():.6f} 小时")
                    print(f"  标准差: {time_diffs_hours.std():.6f} 小时")
                    print(f"  最小间隔: {time_diffs_hours.min():.6f} 小时")
                    print(f"  最大间隔: {time_diffs_hours.max():.6f} 小时")
                    
                    # 检查一致性
                    target = 9.925
                    consistency = (time_diffs_hours.std() < 0.001)
                    accuracy = (abs(time_diffs_hours.mean() - target) < 0.001)
                    
                    print(f"  一致性: {'✅' if consistency else '❌'}")
                    print(f"  准确性: {'✅' if accuracy else '❌'}")
                    
                    if consistency and accuracy:
                        print(f"  🌟 这是最优格式！")
                        
                        # 保存测试数据
                        test_file = f'planet_data/木星_测试_{step_format.replace(".", "_")}.csv'
                        os.makedirs('planet_data', exist_ok=True)
                        
                        # 简单处理数据
                        processed_df = pd.DataFrame()
                        processed_df['julian_date'] = df['datetime_jd']
                        processed_df['date'] = df['datetime_str']
                        processed_df['distance_au'] = df['range']
                        
                        processed_df.to_csv(test_file, index=False)
                        print(f"  测试数据已保存: {test_file}")
            
        except Exception as e:
            print(f"  ❌ 失败: {e}")

def recommend_best_approach():
    """推荐最佳方法"""
    print(f"\n📋 最佳方法推荐")
    print("=" * 60)
    
    print(f"🎯 基于测试结果的建议:")
    print(f"  1. 如果0.41354167d格式有效:")
    print(f"     - 使用最精确的天格式")
    print(f"     - 可能实现接近100%的木星日间隔")
    print(f"     - 需要重新下载完整数据集")
    
    print(f"\n  2. 如果精确格式不支持:")
    print(f"     - 继续使用596m格式")
    print(f"     - 98.5%标准间隔已经足够优秀")
    print(f"     - 通过后处理优化时间网格")
    
    print(f"\n  3. 混合策略:")
    print(f"     - 使用595m和596m交替")
    print(f"     - 补偿累积误差")
    print(f"     - 复杂但可能更精确")
    
    print(f"\n🔬 科学角度评估:")
    print(f"  - 98.5%标准间隔对轨道分析影响微乎其微")
    print(f"  - 1.5%异常间隔主要在日期边界")
    print(f"  - 不会影响开普勒定律验证等科学计算")
    print(f"  - 追求100%可能得不偿失")

def main():
    """主函数"""
    print("🪐 JPL HORIZONS精确木星日格式测试")
    print("=" * 60)
    print("目标：寻找实现100%标准木星日间隔的可能性")
    
    # 测试1：各种格式支持
    test_precise_jupiter_day_formats()
    
    # 测试2：最优格式详细测试
    test_optimal_format()
    
    # 测试3：推荐最佳方法
    recommend_best_approach()
    
    print(f"\n🏆 结论:")
    print("通过测试确定JPL HORIZONS对精确木星日格式的支持程度，")
    print("为实现更高精度的时间间隔提供技术依据。")

if __name__ == "__main__":
    main()
