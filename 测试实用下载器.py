#!/usr/bin/env python3
"""
测试实用精确下载器 - 只下载前5个批次验证
"""

import os
import json
import pandas as pd
import warnings
import ssl
import urllib3
import time
from datetime import datetime

# 配置
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# 代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

def test_practical_downloader():
    """测试实用下载器"""
    print("🧪 测试实用精确下载器")
    print("=" * 60)
    
    # 加载下载计划
    with open('jupiter_practical_download_plan.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    batches = data['batches']
    print(f"✅ 加载了 {len(batches)} 个批次")
    
    # 测试前5个批次
    test_batches = batches[:5]
    print(f"🔬 测试前 {len(test_batches)} 个批次")
    
    results = []
    
    for i, batch in enumerate(test_batches, 1):
        batch_id = batch['batch_id']
        jpl_step = batch['jpl_step']
        start_date = batch['jpl_start_date']
        end_date = batch['jpl_end_date']
        expected_points = batch['expected_points']
        
        print(f"\n📦 批次 {i}/{len(test_batches)}")
        print(f"   批次ID: {batch_id}")
        print(f"   步长: {jpl_step}")
        print(f"   时间范围: {start_date} 到 {end_date}")
        print(f"   期望点数: {expected_points}")
        
        try:
            from astroquery.jplhorizons import Horizons
            
            jupiter = Horizons(
                id='599',
                location='@sun',
                epochs={
                    'start': start_date,
                    'stop': end_date,
                    'step': jpl_step
                }
            )
            
            vectors = jupiter.vectors()
            df = vectors.to_pandas()
            
            if len(df) > 0:
                print(f"   ✅ 成功获取 {len(df)} 个数据点")
                
                # 分析时间间隔
                df['datetime'] = pd.to_datetime(df['datetime_str'], format='A.D. %Y-%b-%d %H:%M:%S.%f')
                df = df.sort_values('datetime')
                
                if len(df) > 1:
                    time_diffs = df['datetime'].diff().dropna()
                    avg_interval_minutes = time_diffs.dt.total_seconds().mean() / 60
                    
                    print(f"   平均间隔: {avg_interval_minutes:.3f} 分钟")
                    print(f"   与木星日差异: {abs(avg_interval_minutes - 595.5):.3f} 分钟")
                    
                    # 检查间隔一致性
                    interval_std = time_diffs.dt.total_seconds().std() / 60
                    print(f"   间隔标准差: {interval_std:.3f} 分钟")
                
                results.append({
                    'batch_id': batch_id,
                    'status': 'success',
                    'points': len(df),
                    'expected': expected_points,
                    'step': jpl_step,
                    'avg_interval_minutes': avg_interval_minutes if len(df) > 1 else None,
                    'coverage_rate': len(df) / expected_points * 100
                })
                
            else:
                print(f"   ⚠️ 无数据返回")
                results.append({
                    'batch_id': batch_id,
                    'status': 'no_data',
                    'points': 0,
                    'expected': expected_points,
                    'step': jpl_step
                })
                
        except Exception as e:
            print(f"   ❌ 下载失败: {e}")
            results.append({
                'batch_id': batch_id,
                'status': 'failed',
                'points': 0,
                'expected': expected_points,
                'step': jpl_step,
                'error': str(e)
            })
        
        print(f"   ⏳ 等待2秒...")
        time.sleep(2)
    
    # 分析测试结果
    print(f"\n📊 测试结果分析:")
    print("=" * 60)
    
    successful = [r for r in results if r['status'] == 'success']
    failed = [r for r in results if r['status'] != 'success']
    
    print(f"成功批次: {len(successful)}/{len(test_batches)}")
    print(f"失败批次: {len(failed)}")
    
    if successful:
        total_points = sum(r['points'] for r in successful)
        total_expected = sum(r['expected'] for r in successful)
        
        print(f"总下载点数: {total_points}")
        print(f"总期望点数: {total_expected}")
        print(f"点数完成率: {total_points/total_expected*100:.2f}%")
        
        # 分析混合策略效果
        steps_595 = [r for r in successful if r['step'] == '595m']
        steps_596 = [r for r in successful if r['step'] == '596m']
        
        print(f"\n🔬 混合策略验证:")
        print(f"595m批次: {len(steps_595)} 个")
        print(f"596m批次: {len(steps_596)} 个")
        
        if steps_595 and steps_596:
            avg_595 = sum(r['avg_interval_minutes'] for r in steps_595 if r['avg_interval_minutes']) / len([r for r in steps_595 if r['avg_interval_minutes']])
            avg_596 = sum(r['avg_interval_minutes'] for r in steps_596 if r['avg_interval_minutes']) / len([r for r in steps_596 if r['avg_interval_minutes']])
            
            print(f"595m平均间隔: {avg_595:.3f} 分钟 (误差: {avg_595-595.5:.3f})")
            print(f"596m平均间隔: {avg_596:.3f} 分钟 (误差: {avg_596-595.5:.3f})")
            
            # 计算加权平均误差
            total_intervals_595 = sum(r['points']-1 for r in steps_595 if r['points'] > 1)
            total_intervals_596 = sum(r['points']-1 for r in steps_596 if r['points'] > 1)
            
            if total_intervals_595 + total_intervals_596 > 0:
                weighted_avg_error = (
                    total_intervals_595 * (avg_595 - 595.5) + 
                    total_intervals_596 * (avg_596 - 595.5)
                ) / (total_intervals_595 + total_intervals_596)
                
                print(f"加权平均误差: {weighted_avg_error:.6f} 分钟")
                
                if abs(weighted_avg_error) < 0.1:
                    print(f"✅ 混合策略效果优秀！")
                elif abs(weighted_avg_error) < 0.3:
                    print(f"📈 混合策略效果良好")
                else:
                    print(f"⚠️ 混合策略效果一般")
        
        print(f"\n✅ 测试成功！实用精确下载器工作正常")
        print("可以继续进行完整下载")
        
    else:
        print(f"\n❌ 测试失败！需要检查配置")
    
    # 保存测试结果
    with open('test_practical_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"测试结果已保存: test_practical_results.json")
    
    return len(successful) == len(test_batches)

def main():
    """主函数"""
    print("🛠️ 实用精确下载器测试")
    print("=" * 60)
    
    success = test_practical_downloader()
    
    if success:
        print(f"\n🎉 测试完全成功！")
        print("实用精确下载方案验证通过，可以执行完整下载")
    else:
        print(f"\n⚠️ 测试部分失败，需要检查问题")

if __name__ == "__main__":
    main()
