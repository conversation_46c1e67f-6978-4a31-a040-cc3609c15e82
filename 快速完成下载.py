#!/usr/bin/env python3
"""
快速完成剩余下载
"""

import subprocess
import time
from datetime import datetime

def quick_download_remaining():
    """快速下载剩余批次"""
    print("🚀 快速完成木星数据下载")
    print("=" * 60)
    
    # 定义下载段落
    segments = [
        (41, 60),
        (61, 80),
        (81, 100),
        (101, 120),
        (121, 144)  # 最后一段到144
    ]
    
    for start, end in segments:
        print(f"\n📦 下载段落: {start} 到 {end}")
        print(f"时间: {datetime.now().strftime('%H:%M:%S')}")
        
        try:
            result = subprocess.run([
                'python', '分段下载器.py', str(start), str(end)
            ], capture_output=True, text=True, timeout=1200)
            
            if result.returncode == 0:
                print(f"✅ 段落 {start}-{end} 完成")
                # 提取成功信息
                lines = result.stdout.split('\n')
                for line in lines:
                    if '段落下载完成' in line or '成功批次' in line or '总数据点' in line:
                        print(f"   {line.strip()}")
            else:
                print(f"❌ 段落 {start}-{end} 失败")
                print(f"错误: {result.stderr}")
        
        except subprocess.TimeoutExpired:
            print(f"⏰ 段落 {start}-{end} 超时")
        
        except Exception as e:
            print(f"❌ 段落 {start}-{end} 异常: {e}")
        
        # 段落间休息
        if end < 144:
            print(f"⏳ 休息10秒...")
            time.sleep(10)
    
    print(f"\n🎉 所有下载段落完成！")

def merge_and_finalize():
    """合并并完成最终处理"""
    print(f"\n🔄 合并并完成最终处理")
    print("=" * 60)
    
    try:
        # 运行合并脚本
        result = subprocess.run([
            'python', '自动下载所有批次.py'
        ], capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ 数据合并完成")
            # 显示关键输出
            lines = result.stdout.split('\n')
            for line in lines:
                if any(keyword in line for keyword in ['合并结果', '数据点', '最终文件', '完成']):
                    print(f"   {line.strip()}")
        else:
            print("❌ 数据合并失败")
            print(f"错误: {result.stderr}")
    
    except Exception as e:
        print(f"❌ 合并异常: {e}")

def main():
    """主函数"""
    print("🪐 木星数据快速完成下载")
    print("=" * 60)
    
    start_time = datetime.now()
    
    # 1. 快速下载剩余批次
    quick_download_remaining()
    
    # 2. 合并并完成
    merge_and_finalize()
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"\n⏱️ 总耗时: {duration}")
    print(f"🎉 木星严格时间序列数据下载完成！")

if __name__ == "__main__":
    main()
